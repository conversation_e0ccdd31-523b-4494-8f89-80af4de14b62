{"__meta": {"id": "01K1G6S8SJV1GVYKMA4JHCSYJE", "datetime": "2025-07-31 13:19:49", "utime": **********.171218, "method": "PUT", "uri": "/admin/catalog/products/edit/195?channel=default&locale=en", "ip": "127.0.0.1"}, "modules": {"count": 5, "modules": [{"name": "Webkul\\Attribute", "models": ["Webkul\\Attribute\\Models\\Attribute (128)", "Webkul\\Attribute\\Models\\AttributeFamily (6)"], "views": [], "queries": [{"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 195", "duration": 3.66, "duration_str": "3.66s", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 1.93, "duration_str": "1.93s", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2 and `attributes`.`code` not in ('color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock')", "duration": 3.46, "duration_str": "3.46s", "connection": "mlk"}, {"sql": "select * from `attributes` where `code` = 'url_key'", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 0.36, "duration_str": "360ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 0.36, "duration_str": "360ms", "connection": "mlk"}, {"sql": "select * from `attributes` where `code` in ('sku', 'name', 'url_key', 'short_description', 'description', 'price', 'weight', 'status', 'tax_category_id')", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "duration": 3.83, "duration_str": "3.83s", "connection": "mlk"}]}, {"name": "Webkul\\Category", "models": [], "views": [], "queries": [{"sql": "select exists(select 1 from `category_translations` where `slug` = '1010-iphone-ip14-pro-max-67' limit 1) as `exists`", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}]}, {"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (6)", "Webkul\\Core\\Models\\Locale (36)", "Webkul\\Core\\Models\\Currency (1)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 1.86, "duration_str": "1.86s", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 195", "duration": 2.03, "duration_str": "2.03s", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 195", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 3.75, "duration_str": "3.75s", "connection": "mlk"}, {"sql": "select * from `locales` where `locales`.`id` = 2 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select * from `currencies` where `currencies`.`id` = 2 limit 1", "duration": 2.08, "duration_str": "2.08s", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 195", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 196", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 197", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}]}, {"name": "Webkul\\Product", "models": ["Webkul\\Product\\Models\\Product (20)", "Webkul\\Product\\Models\\ProductAttributeValue (189)", "Webkul\\Product\\Models\\ProductImage (6)", "Webkul\\Product\\Models\\ProductInventory (2)", "Webkul\\Product\\Models\\ProductFlat (15)"], "views": [], "queries": [{"sql": "select * from `products` where `products`.`id` = '195' limit 1", "duration": 2.29, "duration_str": "2.29s", "connection": "mlk"}, {"sql": "select `id` from `products` where `products`.`parent_id` = 195 and `products`.`parent_id` is not null", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `products` where `sku` = '2034101025509' and `id` <> '195'", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = '1010-iphone-ip14-pro-max-67'", "duration": 2.23, "duration_str": "2.23s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select count(`id`) as aggregate from `product_attribute_values` where `text_value` = '1010 IP14 PRO 6.7' and `attribute_id` = 27 and `product_id` != '195'", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "select exists(select * from `products` where `sku` in ('2034101025509-Red', '2034101025509-Green') and `id` not in (196, 197)) as `exists`", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select exists(select * from `products` where `sku` in ('2034101025509-Red', '2034101025509-Green') and `id` not in (196, 197)) as `exists`", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 195 and `product_attribute_values`.`product_id` is not null", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 195 and `product_attribute_values`.`product_id` is not null", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4462 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4463 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4464 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `json_value` = '[\\\"product\\/195\\/1nGpxRdwhgcH7fH8ODo6e1aAo7xFdCmHXlmsCsqX.webp\\\"]' where `id` = 4464", "duration": 4.11, "duration_str": "4.11s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4465 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4466 limit 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4467 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4468 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4469 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4470 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4471 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4472 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4473 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4474 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 0 where `id` = 4474", "duration": 2.07, "duration_str": "2.07s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4475 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4476 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4477 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4478 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4479 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4479", "duration": 2.08, "duration_str": "2.08s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4480 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4480", "duration": 2.04, "duration_str": "2.04s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4481 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4481", "duration": 1.99, "duration_str": "1.99s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4482 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4482", "duration": 2, "duration_str": "2s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4483 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4483", "duration": 2.04, "duration_str": "2.04s", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 195 and `product_images`.`product_id` is not null order by `position` asc", "duration": 1.61, "duration_str": "1.61s", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`id` = 169 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select `id` from `product_videos` where `product_videos`.`product_id` = 195 and `product_videos`.`product_id` is not null order by `position` asc", "duration": 1.75, "duration_str": "1.75s", "connection": "mlk"}, {"sql": "select `id` from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 195 and `product_customer_group_prices`.`product_id` is not null", "duration": 1.62, "duration_str": "1.62s", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 195 and `products`.`parent_id` is not null", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 196 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 196 and `product_attribute_values`.`product_id` is not null", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4442 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4484 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4445 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4446 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4446", "duration": 2.1, "duration_str": "2.1s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4449 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4450 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_inventories` where (`product_id` = 196 and `inventory_source_id` = 1 and `vendor_id` = 0) limit 1", "duration": 1.7, "duration_str": "1.7s", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 196 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`id` = 170 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 197 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 197 and `product_attribute_values`.`product_id` is not null", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4452 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4485 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4455 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4456 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4456", "duration": 2.11, "duration_str": "2.11s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4459 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4460 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_inventories` where (`product_id` = 197 and `inventory_source_id` = 1 and `vendor_id` = 0) limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 197 and `product_images`.`product_id` is not null order by `position` asc", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `product_images` where `product_images`.`id` = 171 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `products` where `id` = 195 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (195)", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` in (195)", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 195 and `products`.`parent_id` is not null", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 196 and `product_attribute_values`.`product_id` is not null", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 197 and `product_attribute_values`.`product_id` is not null", "duration": 0.26, "duration_str": "260ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 195 and `product_attribute_values`.`product_id` is not null", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 195 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 2.09, "duration_str": "2.09s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 195 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 195 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 195 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.32, "duration_str": "320ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 195 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.46, "duration_str": "460ms", "connection": "mlk"}, {"sql": "select * from `products` where `products`.`parent_id` = 195 and `products`.`parent_id` is not null", "duration": 0.36, "duration_str": "360ms", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 196 and `product_attribute_values`.`product_id` is not null", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 196 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-07-31 13:19:49' where `id` = 979", "duration": 2.15, "duration_str": "2.15s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 196 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.3, "duration_str": "300ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-07-31 13:19:49' where `id` = 980", "duration": 2.21, "duration_str": "2.21s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 196 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-07-31 13:19:49' where `id` = 981", "duration": 2.21, "duration_str": "2.21s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 196 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.33, "duration_str": "330ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-07-31 13:19:49' where `id` = 982", "duration": 2.12, "duration_str": "2.12s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 196 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-07-31 13:19:49' where `id` = 983", "duration": 2.18, "duration_str": "2.18s", "connection": "mlk"}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 197 and `product_attribute_values`.`product_id` is not null", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 197 and `channel` = 'default' and `locale` = 'en') limit 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-07-31 13:19:49' where `id` = 984", "duration": 2.08, "duration_str": "2.08s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 197 and `channel` = 'default' and `locale` = 'it') limit 1", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-07-31 13:19:49' where `id` = 985", "duration": 2.09, "duration_str": "2.09s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 197 and `channel` = 'default' and `locale` = 'de') limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-07-31 13:19:49' where `id` = 986", "duration": 2.07, "duration_str": "2.07s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 197 and `channel` = 'default' and `locale` = 'fr') limit 1", "duration": 0.24, "duration_str": "240ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-07-31 13:19:49' where `id` = 987", "duration": 2.08, "duration_str": "2.08s", "connection": "mlk"}, {"sql": "select * from `product_flat` where (`product_id` = 197 and `channel` = 'default' and `locale` = 'gr') limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "update `product_flat` set `weight` = '0.1', `product_flat`.`updated_at` = '2025-07-31 13:19:49' where `id` = 988", "duration": 2.1, "duration_str": "2.1s", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}]}]}, "messages": {"count": 2, "messages": [{"message": "[13:19:49] LOG.info: 触发产品AI翻译 {\n    \"product_id\": 195\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.031338, "xdebug_link": null, "collector": "log"}, {"message": "[13:19:49] LOG.info: 使用源语言进行翻译 {\n    \"product_id\": 195,\n    \"source_locale\": \"en\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.032411, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.623554, "end": **********.179448, "duration": 0.5558938980102539, "duration_str": "556ms", "measures": [{"label": "Booting", "start": **********.623554, "relative_start": 0, "end": **********.806217, "relative_end": **********.806217, "duration": 0.****************, "duration_str": "183ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.806227, "relative_start": 0.*****************, "end": **********.179449, "relative_end": 1.1920928955078125e-06, "duration": 0.*****************, "duration_str": "373ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.817001, "relative_start": 0.*****************, "end": **********.818544, "relative_end": **********.818544, "duration": 0.001542806625366211, "duration_str": "1.54ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.169601, "relative_start": 0.****************, "end": **********.169856, "relative_end": **********.169856, "duration": 0.0002551078796386719, "duration_str": "255μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 155, "nb_statements": 155, "nb_visible_statements": 155, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.12058000000000002, "accumulated_duration_str": "121ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 55 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.832476, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 0.315}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.835414, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 0.315, "width_percent": 0.133}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.841002, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 0.448, "width_percent": 0.182}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.8426929, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0.63, "width_percent": 0.166}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.843656, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 0.796, "width_percent": 0.116}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.84678, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 0.912, "width_percent": 0.141}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.848701, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 1.053, "width_percent": 0.133}, {"sql": "select * from `products` where `products`.`id` = '195' limit 1", "type": "query", "params": [], "bindings": ["195"], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.854536, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 1.186, "width_percent": 1.899}, {"sql": "select `id` from `products` where `products`.`parent_id` = 195 and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 288}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 313}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 60}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.8610399, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "Configurable.php:288", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 288}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FConfigurable.php&line=288", "ajax": false, "filename": "Configurable.php", "line": "288"}, "connection": "mlk", "explain": null, "start_percent": 3.085, "width_percent": 0.207}, {"sql": "select `attributes`.*, `product_super_attributes`.`product_id` as `pivot_product_id`, `product_super_attributes`.`attribute_id` as `pivot_attribute_id` from `attributes` inner join `product_super_attributes` on `attributes`.`id` = `product_super_attributes`.`attribute_id` where `product_super_attributes`.`product_id` = 195", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 546}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 418}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 87}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.8644881, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 3.292, "width_percent": 3.035}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 552}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 418}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 87}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.8695061, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 6.328, "width_percent": 1.601}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2 and `attributes`.`code` not in ('color', 'price', 'cost', 'special_price', 'special_price_from', 'special_price_to', 'length', 'width', 'height', 'weight', 'manage_stock')", "type": "query", "params": [], "bindings": [2, "color", "price", "cost", "special_price", "special_price_from", "special_price_to", "length", "width", "height", "weight", "manage_stock"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 555}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 418}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 87}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.8722742, "duration": 0.00346, "duration_str": "3.46ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:555", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=555", "ajax": false, "filename": "AbstractType.php", "line": "555"}, "connection": "mlk", "explain": null, "start_percent": 7.928, "width_percent": 2.869}, {"sql": "select count(*) as aggregate from `products` where `sku` = '2034101025509' and `id` <> '195'", "type": "query", "params": [], "bindings": ["2034101025509", "195"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 1029}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 686}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 516}], "start": **********.88005, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 10.798, "width_percent": 0.158}, {"sql": "select exists(select 1 from `category_translations` where `slug` = '1010-iphone-ip14-pro-max-67' limit 1) as `exists`", "type": "query", "params": [], "bindings": ["1010-iphone-ip14-pro-max-67"], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 103}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 77}, {"index": 13, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 52}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}], "start": **********.881581, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ProductCategoryUniqueSlug.php:103", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 103}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FProductCategoryUniqueSlug.php&line=103", "ajax": false, "filename": "ProductCategoryUniqueSlug.php", "line": "103"}, "connection": "mlk", "explain": null, "start_percent": 10.955, "width_percent": 0.158}, {"sql": "select * from `attributes` where `code` = 'url_key'", "type": "query", "params": [], "bindings": ["url_key"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 290}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 104}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 125}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}], "start": **********.883898, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:538", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 538}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=538", "ajax": false, "filename": "BaseRepository.php", "line": "538"}, "connection": "mlk", "explain": null, "start_percent": 11.113, "width_percent": 0.174}, {"sql": "select * from `product_attribute_values` where `attribute_id` = 3 and `text_value` = '1010-iphone-ip14-pro-max-67'", "type": "query", "params": [], "bindings": [3, "1010-iphone-ip14-pro-max-67"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, {"index": 16, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Traits/CacheableRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Traits\\CacheableRepository.php", "line": 315}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}, {"index": 19, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 120}], "start": **********.885237, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:559", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 559}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=559", "ajax": false, "filename": "BaseRepository.php", "line": "559"}, "connection": "mlk", "explain": null, "start_percent": 11.287, "width_percent": 1.849}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 161}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 183}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 120}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 77}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ProductCategoryUniqueSlug.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ProductCategoryUniqueSlug.php", "line": 52}], "start": **********.888609, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "ProductRepository.php:161", "source": {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductRepository.php&line=161", "ajax": false, "filename": "ProductRepository.php", "line": "161"}, "connection": "mlk", "explain": null, "start_percent": 13.137, "width_percent": 0.141}, {"sql": "select count(`id`) as aggregate from `product_attribute_values` where `text_value` = '1010 IP14 PRO 6.7' and `attribute_id` = 27 and `product_id` != '195'", "type": "query", "params": [], "bindings": ["1010 IP14 PRO 6.7", 27, "195"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 231}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Requests/ProductForm.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Requests\\ProductForm.php", "line": 123}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/ClosureValidationRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ClosureValidationRule.php", "line": 63}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/ClosureValidationRule.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\ClosureValidationRule.php", "line": 63}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}], "start": **********.8899138, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "ProductAttributeValueRepository.php:231", "source": {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 231}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductAttributeValueRepository.php&line=231", "ajax": false, "filename": "ProductAttributeValueRepository.php", "line": "231"}, "connection": "mlk", "explain": null, "start_percent": 13.277, "width_percent": 0.199}, {"sql": "select exists(select * from `products` where `sku` in ('2034101025509-Red', '2034101025509-Green') and `id` not in (196, 197)) as `exists`", "type": "query", "params": [], "bindings": ["2034101025509-Red", "2034101025509-Green", 196, 197], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 28}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.891397, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "ConfigurableUniqueSku.php:59", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FConfigurableUniqueSku.php&line=59", "ajax": false, "filename": "ConfigurableUniqueSku.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 13.477, "width_percent": 0.174}, {"sql": "select exists(select * from `products` where `sku` in ('2034101025509-Red', '2034101025509-Green') and `id` not in (196, 197)) as `exists`", "type": "query", "params": [], "bindings": ["2034101025509-Red", "2034101025509-Green", 196, 197], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, {"index": 12, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 28}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 902}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 678}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 481}], "start": **********.8926349, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "ConfigurableUniqueSku.php:59", "source": {"index": 11, "namespace": null, "name": "packages/Webkul/Admin/src/Validations/ConfigurableUniqueSku.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Validations\\ConfigurableUniqueSku.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FValidations%2FConfigurableUniqueSku.php&line=59", "ajax": false, "filename": "ConfigurableUniqueSku.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 13.651, "width_percent": 0.158}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 42}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.894471, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 13.808, "width_percent": 0.158}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}, {"index": 30, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}], "start": **********.895714, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 13.966, "width_percent": 0.124}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Attribute/src/Repositories/AttributeRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Repositories\\AttributeRepository.php", "line": 190}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 529}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 396}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}], "start": **********.896516, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 14.09, "width_percent": 0.299}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 336}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 432}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 25, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}], "start": **********.8991392, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "Translatable.php:164", "source": {"index": 19, "namespace": null, "name": "vendor/astrotomic/laravel-translatable/src/Translatable/Translatable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\astrotomic\\laravel-translatable\\src\\Translatable\\Translatable.php", "line": 164}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fastrotomic%2Flaravel-translatable%2Fsrc%2FTranslatable%2FTranslatable.php&line=164", "ajax": false, "filename": "Translatable.php", "line": "164"}, "connection": "mlk", "explain": null, "start_percent": 14.389, "width_percent": 1.543}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 195 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 436}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 398}, {"index": 24, "namespace": null, "name": "packages/Webkul/Marketing/src/Listeners/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Marketing\\src\\Listeners\\Product.php", "line": 44}, {"index": 29, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 151}], "start": **********.9019911, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 15.931, "width_percent": 0.241}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 70}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.903289, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Repository.php:152", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 152}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=152", "ajax": false, "filename": "Repository.php", "line": "152"}, "connection": "mlk", "explain": null, "start_percent": 16.172, "width_percent": 0.141}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 152}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.904529, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 16.313, "width_percent": 0.124}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.9054701, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 16.437, "width_percent": 0.116}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.906291, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "AttributeFamily.php:53", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Attribute/src/Models/AttributeFamily.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Attribute\\src\\Models\\AttributeFamily.php", "line": 53}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=53", "ajax": false, "filename": "AttributeFamily.php", "line": "53"}, "connection": "mlk", "explain": null, "start_percent": 16.553, "width_percent": 0.299}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 195 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.907538, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 16.852, "width_percent": 0.182}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4462 limit 1", "type": "query", "params": [], "bindings": [4462], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.908667, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 17.034, "width_percent": 0.141}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4463 limit 1", "type": "query", "params": [], "bindings": [4463], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.910725, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 17.175, "width_percent": 0.141}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4464 limit 1", "type": "query", "params": [], "bindings": [4464], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.911969, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 17.316, "width_percent": 0.158}, {"sql": "update `product_attribute_values` set `json_value` = '[\\\"product\\\\/195\\\\/1nGpxRdwhgcH7fH8ODo6e1aAo7xFdCmHXlmsCsqX.webp\\\"]' where `id` = 4464", "type": "query", "params": [], "bindings": ["[\"product\\/195\\/1nGpxRdwhgcH7fH8ODo6e1aAo7xFdCmHXlmsCsqX.webp\"]", 4464], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.9128828, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 17.474, "width_percent": 3.409}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4465 limit 1", "type": "query", "params": [], "bindings": [4465], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.918916, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 20.882, "width_percent": 0.141}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4466 limit 1", "type": "query", "params": [], "bindings": [4466], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.92012, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 21.023, "width_percent": 0.207}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4467 limit 1", "type": "query", "params": [], "bindings": [4467], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.921497, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 21.231, "width_percent": 0.133}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4468 limit 1", "type": "query", "params": [], "bindings": [4468], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.922807, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 21.363, "width_percent": 0.124}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4469 limit 1", "type": "query", "params": [], "bindings": [4469], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.92391, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 21.488, "width_percent": 0.141}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4470 limit 1", "type": "query", "params": [], "bindings": [4470], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.9252229, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 21.629, "width_percent": 0.182}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4471 limit 1", "type": "query", "params": [], "bindings": [4471], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.926613, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 21.811, "width_percent": 0.124}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4472 limit 1", "type": "query", "params": [], "bindings": [4472], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.927727, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 21.936, "width_percent": 0.124}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4473 limit 1", "type": "query", "params": [], "bindings": [4473], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.9288561, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 22.06, "width_percent": 0.133}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4474 limit 1", "type": "query", "params": [], "bindings": [4474], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.930044, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 22.193, "width_percent": 0.166}, {"sql": "update `product_attribute_values` set `boolean_value` = 0 where `id` = 4474", "type": "query", "params": [], "bindings": [0, 4474], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.931111, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 22.359, "width_percent": 1.717}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4475 limit 1", "type": "query", "params": [], "bindings": [4475], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.934409, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 24.075, "width_percent": 0.141}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4476 limit 1", "type": "query", "params": [], "bindings": [4476], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.935586, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 24.216, "width_percent": 0.133}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4477 limit 1", "type": "query", "params": [], "bindings": [4477], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.936693, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 24.349, "width_percent": 0.141}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4478 limit 1", "type": "query", "params": [], "bindings": [4478], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.937836, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 24.49, "width_percent": 0.149}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4479 limit 1", "type": "query", "params": [], "bindings": [4479], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.939136, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 24.639, "width_percent": 0.149}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4479", "type": "query", "params": [], "bindings": [1, 4479], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.9400961, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 24.789, "width_percent": 1.725}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4480 limit 1", "type": "query", "params": [], "bindings": [4480], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.943338, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 26.514, "width_percent": 0.141}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4480", "type": "query", "params": [], "bindings": [1, 4480], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.94422, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 26.655, "width_percent": 1.692}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4481 limit 1", "type": "query", "params": [], "bindings": [4481], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.947383, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 28.346, "width_percent": 0.166}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4481", "type": "query", "params": [], "bindings": [1, 4481], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.948343, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 28.512, "width_percent": 1.65}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4482 limit 1", "type": "query", "params": [], "bindings": [4482], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.951541, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 30.163, "width_percent": 0.124}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4482", "type": "query", "params": [], "bindings": [1, 4482], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.9523928, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 30.287, "width_percent": 1.659}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4483 limit 1", "type": "query", "params": [], "bindings": [4483], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.955507, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 31.946, "width_percent": 0.158}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4483", "type": "query", "params": [], "bindings": [1, 4483], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 167}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.9566002, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 32.103, "width_percent": 1.692}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 195", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 173}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9597342, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:173", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 173}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=173", "ajax": false, "filename": "AbstractType.php", "line": "173"}, "connection": "mlk", "explain": null, "start_percent": 33.795, "width_percent": 1.692}, {"sql": "select * from `product_categories` where `product_categories`.`product_id` = 195", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 179}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.963404, "duration": 0.00176, "duration_str": "1.76ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:179", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 179}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=179", "ajax": false, "filename": "AbstractType.php", "line": "179"}, "connection": "mlk", "explain": null, "start_percent": 35.487, "width_percent": 1.46}, {"sql": "select * from `product_up_sells` where `product_up_sells`.`parent_id` = 195", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 181}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.966155, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:181", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 181}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=181", "ajax": false, "filename": "AbstractType.php", "line": "181"}, "connection": "mlk", "explain": null, "start_percent": 36.946, "width_percent": 1.344}, {"sql": "select * from `product_cross_sells` where `product_cross_sells`.`parent_id` = 195", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 183}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.968688, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:183", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 183}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=183", "ajax": false, "filename": "AbstractType.php", "line": "183"}, "connection": "mlk", "explain": null, "start_percent": 38.29, "width_percent": 1.402}, {"sql": "select * from `product_relations` where `product_relations`.`parent_id` = 195", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 185}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.971241, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "AbstractType.php:185", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FAbstractType.php&line=185", "ajax": false, "filename": "AbstractType.php", "line": "185"}, "connection": "mlk", "explain": null, "start_percent": 39.691, "width_percent": 1.418}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 195 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 189}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.9739249, "duration": 0.00161, "duration_str": "1.61ms", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 41.11, "width_percent": 1.335}, {"sql": "select * from `product_images` where `product_images`.`id` = 169 limit 1", "type": "query", "params": [], "bindings": [169], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 80}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 189}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.976372, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 42.445, "width_percent": 0.141}, {"sql": "select `id` from `product_videos` where `product_videos`.`product_id` = 195 and `product_videos`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 191}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.977433, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 42.586, "width_percent": 1.451}, {"sql": "select `id` from `product_customer_group_prices` where `product_customer_group_prices`.`product_id` = 195 and `product_customer_group_prices`.`product_id` is not null", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductCustomerGroupPriceRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductCustomerGroupPriceRepository.php", "line": 24}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/AbstractType.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\AbstractType.php", "line": 193}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 134}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.979991, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "ProductCustomerGroupPriceRepository.php:24", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductCustomerGroupPriceRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductCustomerGroupPriceRepository.php", "line": 24}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductCustomerGroupPriceRepository.php&line=24", "ajax": false, "filename": "ProductCustomerGroupPriceRepository.php", "line": "24"}, "connection": "mlk", "explain": null, "start_percent": 44.037, "width_percent": 1.344}, {"sql": "select * from `attributes` where `code` in ('sku', 'name', 'url_key', 'short_description', 'description', 'price', 'weight', 'status', 'tax_category_id')", "type": "query", "params": [], "bindings": ["sku", "name", "url_key", "short_description", "description", "price", "weight", "status", "tax_category_id"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.9825082, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 45.381, "width_percent": 0.224}, {"sql": "select * from `products` where `products`.`parent_id` = 195 and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 145}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.983635, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 45.605, "width_percent": 0.141}, {"sql": "select * from `products` where `products`.`id` = 196 limit 1", "type": "query", "params": [], "bindings": [196], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 234}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.9845572, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 45.746, "width_percent": 0.116}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 196 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [196], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.985526, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 45.862, "width_percent": 0.158}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4442 limit 1", "type": "query", "params": [], "bindings": [4442], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.986545, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 46.019, "width_percent": 0.124}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4484 limit 1", "type": "query", "params": [], "bindings": [4484], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.987647, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 46.144, "width_percent": 0.182}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4445 limit 1", "type": "query", "params": [], "bindings": [4445], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.988876, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 46.326, "width_percent": 0.133}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4446 limit 1", "type": "query", "params": [], "bindings": [4446], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.989993, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 46.459, "width_percent": 0.141}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4446", "type": "query", "params": [], "bindings": [1, 4446], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.990978, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 46.6, "width_percent": 1.742}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4449 limit 1", "type": "query", "params": [], "bindings": [4449], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.99425, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 48.341, "width_percent": 0.158}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4450 limit 1", "type": "query", "params": [], "bindings": [4450], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.995391, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 48.499, "width_percent": 0.149}, {"sql": "select * from `product_inventories` where (`product_id` = 196 and `inventory_source_id` = 1 and `vendor_id` = 0) limit 1", "type": "query", "params": [], "bindings": [196, 1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductInventoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductInventoryRepository.php", "line": 28}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 240}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.996612, "duration": 0.0017, "duration_str": "1.7ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 48.648, "width_percent": 1.41}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 196 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [196], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 242}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.999451, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 50.058, "width_percent": 0.149}, {"sql": "select * from `product_images` where `product_images`.`id` = 170 limit 1", "type": "query", "params": [], "bindings": [170], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 80}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 242}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.000408, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 50.207, "width_percent": 0.133}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.001412, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 50.34, "width_percent": 0.133}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 195", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.002319, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 50.473, "width_percent": 1.684}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 196", "type": "query", "params": [], "bindings": [196], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.005253, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Configurable.php:244", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FConfigurable.php&line=244", "ajax": false, "filename": "Configurable.php", "line": "244"}, "connection": "mlk", "explain": null, "start_percent": 52.156, "width_percent": 0.158}, {"sql": "select * from `products` where `products`.`id` = 197 limit 1", "type": "query", "params": [], "bindings": [197], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 234}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 23, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.00626, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "Repository.php:135", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Core/src/Eloquent/Repository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Eloquent\\Repository.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FEloquent%2FRepository.php&line=135", "ajax": false, "filename": "Repository.php", "line": "135"}, "connection": "mlk", "explain": null, "start_percent": 52.314, "width_percent": 0.182}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = 197 and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [197], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 116}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.0075579, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 52.496, "width_percent": 0.216}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4452 limit 1", "type": "query", "params": [], "bindings": [4452], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.008857, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 52.712, "width_percent": 0.133}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4485 limit 1", "type": "query", "params": [], "bindings": [4485], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.010103, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 52.845, "width_percent": 0.174}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4455 limit 1", "type": "query", "params": [], "bindings": [4455], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.011365, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 53.019, "width_percent": 0.141}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4456 limit 1", "type": "query", "params": [], "bindings": [4456], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.012543, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 53.16, "width_percent": 0.133}, {"sql": "update `product_attribute_values` set `boolean_value` = 1 where `id` = 4456", "type": "query", "params": [], "bindings": [1, 4456], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.013427, "duration": 0.00211, "duration_str": "2.11ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 53.292, "width_percent": 1.75}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4459 limit 1", "type": "query", "params": [], "bindings": [4459], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.016864, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 55.042, "width_percent": 0.133}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`id` = 4460 limit 1", "type": "query", "params": [], "bindings": [4460], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductAttributeValueRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductAttributeValueRepository.php", "line": 182}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 238}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.017967, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 55.175, "width_percent": 0.158}, {"sql": "select * from `product_inventories` where (`product_id` = 197 and `inventory_source_id` = 1 and `vendor_id` = 0) limit 1", "type": "query", "params": [], "bindings": [197, 1, 0], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductInventoryRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductInventoryRepository.php", "line": 28}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 240}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.018997, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:734", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 734}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=734", "ajax": false, "filename": "BaseRepository.php", "line": "734"}, "connection": "mlk", "explain": null, "start_percent": 55.333, "width_percent": 0.166}, {"sql": "select `id` from `product_images` where `product_images`.`product_id` = 197 and `product_images`.`product_id` is not null order by `position` asc", "type": "query", "params": [], "bindings": [197], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, {"index": 18, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 242}, {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 21, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.019984, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "ProductMediaRepository.php:50", "source": {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 50}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FRepositories%2FProductMediaRepository.php&line=50", "ajax": false, "filename": "ProductMediaRepository.php", "line": "50"}, "connection": "mlk", "explain": null, "start_percent": 55.498, "width_percent": 0.166}, {"sql": "select * from `product_images` where `product_images`.`id` = 171 limit 1", "type": "query", "params": [], "bindings": [171], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductMediaRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductMediaRepository.php", "line": 80}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 242}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}], "start": **********.020933, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 55.664, "width_percent": 0.141}, {"sql": "select * from `products` where `products`.`id` = 195 limit 1", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 24, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 25, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.022005, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 20, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 55.805, "width_percent": 0.158}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = 195", "type": "query", "params": [], "bindings": [195], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, {"index": 21, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, {"index": 22, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 23, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 24, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}], "start": **********.022979, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "Product.php:404", "source": {"index": 19, "namespace": null, "name": "packages/Webkul/Product/src/Models/Product.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Models\\Product.php", "line": 404}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=404", "ajax": false, "filename": "Product.php", "line": "404"}, "connection": "mlk", "explain": null, "start_percent": 55.963, "width_percent": 0.199}, {"sql": "select * from `product_channels` where `product_channels`.`product_id` = 197", "type": "query", "params": [], "bindings": [197], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, {"index": 16, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 166}, {"index": 17, "namespace": null, "name": "packages/Webkul/Product/src/Repositories/ProductRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Repositories\\ProductRepository.php", "line": 72}, {"index": 18, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\Catalog\\ProductController.php", "line": 153}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.023962, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "Configurable.php:244", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Product/src/Type/Configurable.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Product\\src\\Type\\Configurable.php", "line": 244}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FType%2FConfigurable.php&line=244", "ajax": false, "filename": "Configurable.php", "line": "244"}, "connection": "mlk", "explain": null, "start_percent": 56.162, "width_percent": 0.141}, {"sql": "select * from `products` where `id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0250049, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 56.303, "width_percent": 0.158}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.025418, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 56.46, "width_percent": 0.182}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` in (195)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.025848, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 56.643, "width_percent": 0.216}, {"sql": "select * from `products` where `products`.`parent_id` in (195)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.026402, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 56.859, "width_percent": 0.174}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values (?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.04443, "duration": 0.00414, "duration_str": "4.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 57.033, "width_percent": 3.433}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values (?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.049901, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 60.466, "width_percent": 1.808}, {"sql": "select * from `products` where `products`.`parent_id` = ? and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.053872, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 62.274, "width_percent": 0.265}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.05444, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 62.539, "width_percent": 0.232}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.056638, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 62.772, "width_percent": 3.11}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.060789, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 65.882, "width_percent": 0.224}, {"sql": "select * from `locales` where `locales`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.06287, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 66.105, "width_percent": 0.182}, {"sql": "select * from `attribute_families` where `attribute_families`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.063471, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 66.288, "width_percent": 0.158}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0653472, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 66.446, "width_percent": 0.241}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0659478, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 66.686, "width_percent": 0.216}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.068147, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 66.902, "width_percent": 0.216}, {"sql": "select * from `currencies` where `currencies`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.07443, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 67.117, "width_percent": 1.725}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'mlk' and table_name = 'product_flat' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.079278, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 68.842, "width_percent": 0.556}, {"sql": "select `attributes`.* from `attributes` inner join `attribute_group_mappings` on `attributes`.`id` = `attribute_group_mappings`.`attribute_id` inner join `attribute_groups` on `attribute_group_mappings`.`attribute_group_id` = `attribute_groups`.`id` inner join `attribute_families` on `attribute_groups`.`attribute_family_id` = `attribute_families`.`id` where `attribute_families`.`id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.08022, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 69.398, "width_percent": 3.176}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.084502, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 72.574, "width_percent": 0.257}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.085078, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 72.831, "width_percent": 0.265}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0870569, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 73.097, "width_percent": 0.216}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.087645, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 73.312, "width_percent": 1.733}, {"sql": "select column_name as `name`, data_type as `type_name`, column_type as `type`, collation_name as `collation`, is_nullable as `nullable`, column_default as `default`, column_comment as `comment`, generation_expression as `expression`, extra as `extra` from information_schema.columns where table_schema = 'mlk' and table_name = 'product_flat' order by ordinal_position asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.08992, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 75.046, "width_percent": 0.556}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.094002, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 75.601, "width_percent": 0.232}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.097147, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 75.833, "width_percent": 0.265}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.10032, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.099, "width_percent": 0.265}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.103542, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.364, "width_percent": 0.381}, {"sql": "select * from `products` where `products`.`parent_id` = ? and `products`.`parent_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.108279, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 76.746, "width_percent": 0.299}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.108907, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.044, "width_percent": 0.249}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1094332, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.293, "width_percent": 0.241}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1111572, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.534, "width_percent": 0.224}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1117492, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.758, "width_percent": 0.224}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1141388, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 77.981, "width_percent": 1.783}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.116588, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 79.764, "width_percent": 0.249}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.119218, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 80.013, "width_percent": 1.833}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.121798, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 81.846, "width_percent": 0.241}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.124573, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 82.087, "width_percent": 1.833}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.127137, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 83.919, "width_percent": 0.274}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.12969, "duration": 0.00212, "duration_str": "2.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 84.193, "width_percent": 1.758}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.132136, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 85.951, "width_percent": 0.257}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1347518, "duration": 0.00218, "duration_str": "2.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 86.208, "width_percent": 1.808}, {"sql": "select `channels`.*, `product_channels`.`product_id` as `pivot_product_id`, `product_channels`.`channel_id` as `pivot_channel_id` from `channels` inner join `product_channels` on `channels`.`id` = `product_channels`.`channel_id` where `product_channels`.`product_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.137375, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.016, "width_percent": 0.257}, {"sql": "select * from `product_attribute_values` where `product_attribute_values`.`product_id` = ? and `product_attribute_values`.`product_id` is not null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.137984, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.273, "width_percent": 0.224}, {"sql": "select `locales`.*, `channel_locales`.`channel_id` as `pivot_channel_id`, `channel_locales`.`locale_id` as `pivot_locale_id` from `locales` inner join `channel_locales` on `locales`.`id` = `channel_locales`.`locale_id` where `channel_locales`.`channel_id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1398718, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.497, "width_percent": 0.191}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.140399, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.688, "width_percent": 0.207}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1428761, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 88.895, "width_percent": 1.725}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1452608, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.62, "width_percent": 0.207}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.147603, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 90.828, "width_percent": 1.733}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.149962, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.561, "width_percent": 0.191}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1523612, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 92.752, "width_percent": 1.717}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1547298, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 94.468, "width_percent": 0.199}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.157175, "duration": 0.0020800000000000003, "duration_str": "2.08ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 94.667, "width_percent": 1.725}, {"sql": "select * from `product_flat` where (`product_id` = ? and `channel` = ? and `locale` = ?) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.159506, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 96.392, "width_percent": 0.166}, {"sql": "update `product_flat` set `weight` = ?, `product_flat`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.161719, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 96.558, "width_percent": 1.742}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values (?, ?, ?, ?, ?, ?)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1656811, "duration": 0.0020499999999999997, "duration_str": "2.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "mlk", "explain": null, "start_percent": 98.3, "width_percent": 1.7}]}, "models": {"data": {"Webkul\\Product\\Models\\ProductAttributeValue": {"value": 189, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductAttributeValue.php&line=1", "ajax": false, "filename": "ProductAttributeValue.php", "line": "?"}}, "Webkul\\Attribute\\Models\\Attribute": {"value": 128, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "Webkul\\Core\\Models\\Locale": {"value": 36, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FLocale.php&line=1", "ajax": false, "filename": "Locale.php", "line": "?"}}, "Webkul\\Product\\Models\\Product": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductFlat": {"value": 15, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductFlat.php&line=1", "ajax": false, "filename": "ProductFlat.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\Attribute\\Models\\AttributeFamily": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAttribute%2Fsrc%2FModels%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductImage": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductImage.php&line=1", "ajax": false, "filename": "ProductImage.php", "line": "?"}}, "Webkul\\Product\\Models\\ProductInventory": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FProduct%2Fsrc%2FModels%2FProductInventory.php&line=1", "ajax": false, "filename": "ProductInventory.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Webkul\\Core\\Models\\Currency": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}}, "count": 411, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/catalog/products/edit/195?channel=default&locale=en", "action_name": "admin.catalog.products.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@update", "uri": "PUT admin/catalog/products/edit/{id}", "controller": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@update<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=149\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/catalog/products", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FCatalog%2FProductController.php&line=149\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/Catalog/ProductController.php:149-160</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "564ms", "peak_memory": "46MB", "response": "Redirect to http://mlk.test/admin/catalog/products", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1868793654 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>channel</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1868793654\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-231221996 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">PUT</span>\"\n  \"<span class=sf-dump-key>channel</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"13 characters\">2034101025509</span>\"\n  \"<span class=sf-dump-key>group_code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1010</span>\"\n  \"<span class=sf-dump-key>product_number</span>\" => \"<span class=sf-dump-str title=\"17 characters\">1010 IP14 PRO 6.7</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">1010 IPHONE IP14 PRO MAX 6.7</span>\"\n  \"<span class=sf-dump-key>url_key</span>\" => \"<span class=sf-dump-str title=\"27 characters\">1010-iphone-ip14-pro-max-67</span>\"\n  \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"3 characters\">371</span>\"\n  \"<span class=sf-dump-key>device</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">390</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>profile</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>DROP_rating</span>\" => \"<span class=sf-dump-str title=\"2 characters\">31</span>\"\n  \"<span class=sf-dump-key>short_description</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Super Retina XDR Display</span>\"\n  \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"781 characters\">&lt;p&gt;Experience peak performance with the&amp;nbsp;&lt;strong&gt;iPhone 14 Pro Max&lt;/strong&gt;, featuring a stunning&amp;nbsp;&lt;strong&gt;6.7-inch Super Retina XDR display&lt;/strong&gt;&amp;nbsp;with ProMotion for smooth visuals. Powered by the&amp;nbsp;&lt;strong&gt;A16 Bionic chip&lt;/strong&gt;, it delivers lightning-fast speed and efficiency. Capture pro-quality photos with the advanced&amp;nbsp;&lt;strong&gt;48MP Triple-Camera System&lt;/strong&gt;, enhanced by&amp;nbsp;&lt;strong&gt;Cinematic Mode&lt;/strong&gt;&amp;nbsp;and&amp;nbsp;&lt;strong&gt;Photonic Engine&lt;/strong&gt;. Enjoy all-day battery life,&amp;nbsp;&lt;strong&gt;Dynamic Island&lt;/strong&gt;&amp;nbsp;for interactive alerts, and&amp;nbsp;&lt;strong&gt;Ceramic Shield&lt;/strong&gt;&amp;nbsp;for durability. With&amp;nbsp;&lt;strong&gt;5G connectivity&lt;/strong&gt;&amp;nbsp;and&amp;nbsp;&lt;strong&gt;iOS 16&lt;/strong&gt;, it&amp;rsquo;s the ultimate iPhone for power users.&lt;/p&gt;</span>\"\n  \"<span class=sf-dump-key>meta_title</span>\" => \"\"\n  \"<span class=sf-dump-key>meta_keywords</span>\" => \"\"\n  \"<span class=sf-dump-key>meta_description</span>\" => \"\"\n  \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-key>169</span> => \"\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>scene</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>product/195/1nGpxRdwhgcH7fH8ODo6e1aAo7xFdCmHXlmsCsqX.webp</span>\" => \"\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>variants</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>196</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"17 characters\">2034101025509-Red</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">1010 IPHONE IP14 PRO MAX 6.7</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">2.7000</span>\"\n      \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.1</span>\"\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>inventories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>170</span> => \"\"\n        </samp>]\n      </samp>]\n    </samp>]\n    <span class=sf-dump-key>197</span> => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2034101025509-Green</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"34 characters\">1010 IPHONE IP14 PRO MAX 6.7 Green</span>\"\n      \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"6 characters\">2.7000</span>\"\n      \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"3 characters\">0.1</span>\"\n      \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str>2</span>\"\n      \"<span class=sf-dump-key>inventories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>images</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>files</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-key>171</span> => \"\"\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>tax_category_id</span>\" => \"<span class=sf-dump-str>2</span>\"\n  \"<span class=sf-dump-key>new</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>featured</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>visible_individually</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>guest_checkout</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>channels</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>categories</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">13</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-231221996\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-716692670 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IkxacUFEdy9LTVdRTkphc0U4SFYycUE9PSIsInZhbHVlIjoiWUpNZEFVN2NFSDNISGlBTGVZL2s1QlJwT1lPOTFSS2E0V3ZEYTFkVUk1aVhReHAyNUI2d3AzWDRTckVOTUhjOHRTUm9aY1l5bmRUcXMwOE1zMVlER25qbEpkU0o3OWQ5SkhERVE0cUFqbHhrWVhJeE5NTzllRWpLbzdKWVVaeEkiLCJtYWMiOiIzOTVhNTRhYjViNDFjZWI3YTBkNTBhNWZlNjIwY2M2NWFkMzc5YzdlMjdmZWI1YWI2OGUxNzg3NDE0ZDRhYjFjIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IjlVRGo2RDR1WUFoSE1sd01lZ3ZCNkE9PSIsInZhbHVlIjoiV2UvZ0VhSEdxblVOM3BoajRFQ2dDWXpCaVowOCt1MmNTMUl2ZitTdUNuM3Z1bWlIcHVFWVM5b3VFZmpTMUxTcmc2cVBJb3ZNYmN6dXo4VEg3WHpnUnpaaGkzWkw3SVd6dmE3NW4xYS9TR2ZKd0pEOGY0OHJ1YUw5UmhVY0RnVnciLCJtYWMiOiI4MWRjYWM4OWZiMWFlY2IxNGM5YmVmZTRkNjg5YTg2NjQwYWIxMmZmMmVjYTBjMGVkNjhhMDUwNzg3OWYwMDBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"73 characters\">http://mlk.test/admin/catalog/products/edit/195?channel=default&amp;locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryQkZ4uUQXJfh2RRg5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">6310</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-716692670\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1329663118 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mRElLYzaB8skVInGoMo42QqvMAEc6r8ga9pHAY8N</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1329663118\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1891790725 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:19:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"38 characters\">http://mlk.test/admin/catalog/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1891790725\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-119205956 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"73 characters\">http://mlk.test/admin/catalog/products/edit/195?channel=default&amp;locale=en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#20135;&#21697;&#26356;&#26032;&#25104;&#21151;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-119205956\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/catalog/products/edit/195?channel=default&locale=en", "action_name": "admin.catalog.products.update", "controller_action": "Webkul\\Admin\\Http\\Controllers\\Catalog\\ProductController@update"}, "badge": "302 Found"}}