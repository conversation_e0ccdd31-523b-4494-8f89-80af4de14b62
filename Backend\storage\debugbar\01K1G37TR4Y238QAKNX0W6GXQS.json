{"__meta": {"id": "01K1G37TR4Y238QAKNX0W6GXQS", "datetime": "2025-07-31 12:17:52", "utime": **********.005119, "method": "POST", "uri": "/admin/configuration/general/magic_ai", "ip": "127.0.0.1"}, "modules": {"count": 2, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\CoreConfig (26)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.enabled' and `channel_code` = 'default'", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 22 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_key' and `channel_code` = 'default'", "duration": 0.49, "duration_str": "490ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 23 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.organization' and `channel_code` = 'default'", "duration": 0.54, "duration_str": "540ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 24 limit 1", "duration": 0.27, "duration_str": "270ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_domain' and `channel_code` = 'default'", "duration": 0.53, "duration_str": "530ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 25 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.enabled'", "duration": 0.45, "duration_str": "450ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 26 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_short_description_prompt' and `locale_code` = 'en'", "duration": 0.42, "duration_str": "420ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 35 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "update `core_config` set `value` = '请生成简单的一句话描述', `core_config`.`updated_at` = '2025-07-31 12:17:51' where `id` = 35", "duration": 3.59, "duration_str": "3.59s", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_description_prompt' and `locale_code` = 'en'", "duration": 0.4, "duration_str": "400ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 36 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `core_config` set `value` = '请优化生成产品描述。', `core_config`.`updated_at` = '2025-07-31 12:17:51' where `id` = 36", "duration": 2.4, "duration_str": "2.4s", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.category_description_prompt' and `locale_code` = 'en'", "duration": 0.46, "duration_str": "460ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 37 limit 1", "duration": 0.31, "duration_str": "310ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.cms_page_content_prompt' and `locale_code` = 'en'", "duration": 0.44, "duration_str": "440ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 38 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "update `core_config` set `value` = '请优化内容描述。', `core_config`.`updated_at` = '2025-07-31 12:17:51' where `id` = 38", "duration": 2.13, "duration_str": "2.13s", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.image_generation.enabled' and `channel_code` = 'default'", "duration": 0.45, "duration_str": "450ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 31 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.review_translation.enabled' and `channel_code` = 'default'", "duration": 0.53, "duration_str": "530ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 32 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.enabled' and `channel_code` = 'default'", "duration": 0.47, "duration_str": "470ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 33 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.prompt' and `locale_code` = 'en' and `channel_code` = 'default'", "duration": 0.43, "duration_str": "430ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 39 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.450006, "end": **********.012352, "duration": 1.5623459815979004, "duration_str": "1.56s", "measures": [{"label": "Booting", "start": **********.450006, "relative_start": 0, "end": **********.717239, "relative_end": **********.717239, "duration": 0.*****************, "duration_str": "267ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.717251, "relative_start": 0.****************, "end": **********.012353, "relative_end": 9.5367431640625e-07, "duration": 1.****************, "duration_str": "1.3s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.732294, "relative_start": 0.****************, "end": **********.735268, "relative_end": **********.735268, "duration": 0.0029740333557128906, "duration_str": "2.97ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.002418, "relative_start": 1.****************, "end": **********.002766, "relative_end": **********.002766, "duration": 0.0003478527069091797, "duration_str": "348μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 36, "nb_statements": 36, "nb_visible_statements": 36, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01826, "accumulated_duration_str": "18.26ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.776269, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 2.848}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.781028, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 2.848, "width_percent": 1.095}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.7886848, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 3.943, "width_percent": 1.26}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.791081, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 5.203, "width_percent": 1.314}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.792454, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 6.517, "width_percent": 0.767}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.7964, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 7.284, "width_percent": 1.095}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.79875, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 8.379, "width_percent": 1.15}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.802205, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 9.529, "width_percent": 1.588}, {"sql": "select * from `core_config` where `core_config`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.80339, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 11.117, "width_percent": 0.986}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_key' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.api_key", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.899746, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 12.103, "width_percent": 2.683}, {"sql": "select * from `core_config` where `core_config`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.902961, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 14.786, "width_percent": 0.986}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.organization' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.organization", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.993865, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 15.772, "width_percent": 2.957}, {"sql": "select * from `core_config` where `core_config`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.9954512, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 18.729, "width_percent": 1.479}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_domain' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.api_domain", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.090903, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 20.208, "width_percent": 2.903}, {"sql": "select * from `core_config` where `core_config`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.092374, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 23.111, "width_percent": 0.931}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.enabled'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.177942, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:79", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=79", "ajax": false, "filename": "CoreConfigRepository.php", "line": "79"}, "connection": "mlk", "explain": null, "start_percent": 24.042, "width_percent": 2.464}, {"sql": "select * from `core_config` where `core_config`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.179368, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 26.506, "width_percent": 0.876}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_short_description_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.product_short_description_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2619739, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 27.382, "width_percent": 2.3}, {"sql": "select * from `core_config` where `core_config`.`id` = 35 limit 1", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.263321, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 29.682, "width_percent": 0.876}, {"sql": "update `core_config` set `value` = '请生成简单的一句话描述', `core_config`.`updated_at` = '2025-07-31 12:17:51' where `id` = 35", "type": "query", "params": [], "bindings": ["请生成简单的一句话描述", "2025-07-31 12:17:51", 35], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.264354, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 30.559, "width_percent": 19.66}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_description_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.product_description_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.351456, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 50.219, "width_percent": 2.191}, {"sql": "select * from `core_config` where `core_config`.`id` = 36 limit 1", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.352991, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 52.41, "width_percent": 0.986}, {"sql": "update `core_config` set `value` = '请优化生成产品描述。', `core_config`.`updated_at` = '2025-07-31 12:17:51' where `id` = 36", "type": "query", "params": [], "bindings": ["请优化生成产品描述。", "2025-07-31 12:17:51", 36], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.353964, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 53.395, "width_percent": 13.143}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.category_description_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.category_description_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.4428601, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 66.539, "width_percent": 2.519}, {"sql": "select * from `core_config` where `core_config`.`id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.444397, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 69.058, "width_percent": 1.698}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.cms_page_content_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.cms_page_content_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.5331972, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 70.756, "width_percent": 2.41}, {"sql": "select * from `core_config` where `core_config`.`id` = 38 limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.534631, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 73.165, "width_percent": 0.876}, {"sql": "update `core_config` set `value` = '请优化内容描述。', `core_config`.`updated_at` = '2025-07-31 12:17:51' where `id` = 38", "type": "query", "params": [], "bindings": ["请优化内容描述。", "2025-07-31 12:17:51", 38], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.535615, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 74.042, "width_percent": 11.665}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.image_generation.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.image_generation.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.621773, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 85.706, "width_percent": 2.464}, {"sql": "select * from `core_config` where `core_config`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.623193, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 88.171, "width_percent": 0.931}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.review_translation.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.review_translation.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.727782, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 89.102, "width_percent": 2.903}, {"sql": "select * from `core_config` where `core_config`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.729882, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 92.004, "width_percent": 1.095}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.checkout_message.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.8299382, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 93.1, "width_percent": 2.574}, {"sql": "select * from `core_config` where `core_config`.`id` = 33 limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.831382, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 95.674, "width_percent": 0.767}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.prompt' and `locale_code` = 'en' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.checkout_message.prompt", "en", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 63}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.9144778, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:63", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=63", "ajax": false, "filename": "CoreConfigRepository.php", "line": "63"}, "connection": "mlk", "explain": null, "start_percent": 96.44, "width_percent": 2.355}, {"sql": "select * from `core_config` where `core_config`.`id` = 39 limit 1", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.9158359, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 98.795, "width_percent": 1.205}]}, "models": {"data": {"Webkul\\Core\\Models\\CoreConfig": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCoreConfig.php&line=1", "ajax": false, "filename": "CoreConfig.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 29, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/configuration/general/magic_ai", "action_name": "admin.configuration.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store", "uri": "POST admin/configuration/{slug?}/{slug2?}", "controller": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FConfigurationController.php&line=53\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/configuration/{slug?}/{slug2?}", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FConfigurationController.php&line=53\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php:53-128</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "1.57s", "peak_memory": "42MB", "response": "Redirect to http://mlk.test/admin/configuration/general/magic_ai", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1252379534 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1252379534\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-372303281 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>channel</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>keys</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"582 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.image-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u56fe\\u50cf\\u4e0a\\u4f20\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528DALL-E\\u751f\\u6210\\u56fe\\u50cf\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u56fe\\u50cf\\u4e0a\\u4f20\\u751f\\u6210\\u56fe\\u50cf\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.image_generation&quot;,&quot;name&quot;:&quot;\\u56fe\\u50cf\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"3273 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama-groq&quot;,&quot;value&quot;:&quot;llama3-8b-8192&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u6216\\u8bbf\\u5ba2\\u63d0\\u4f9b\\u5c06\\u5ba2\\u6237\\u8bc4\\u8bba\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u7684\\u9009\\u9879\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u8bc4\\u8bba\\u9875\\u9762\\uff0c\\u5982\\u679c\\u8bc4\\u8bba\\u4e0d\\u662f\\u82f1\\u8bed\\uff0c\\u60a8\\u4f1a\\u627e\\u5230\\u201c\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u201d\\u6309\\u94ae\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.review_translation&quot;,&quot;name&quot;:&quot;\\u8bc4\\u8bba\\u7ffb\\u8bd1&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3273 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama-groq&quot;,&quot;value&quot;:&quot;llama3-8b-8192&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u6216\\u8bbf\\u5ba2\\u63d0\\u4f9b\\u5c06\\u5ba2\\u6237\\u8bc4\\u8bba\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u7684\\u9009\\u9879\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u8bc4\\u8bba\\u9875\\u9762\\uff0c\\u5982\\u679c\\u8bc4\\u8bba\\u4e0d\\u662f\\u82f1\\u8bed\\uff0c\\u60a8\\u4f1a\\u627e\\u5230\\u201c\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u201d\\u6309\\u94ae\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.review_translation&quot;,&quot;name&quot;:&quot;\\u8bc4\\u8bba\\u7ffb\\u8bd1&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>general</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>magic_ai</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>settings</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>api_key</span>\" => \"<span class=sf-dump-str title=\"164 characters\">********************************************************************************************************************************************************************</span>\"\n        \"<span class=sf-dump-key>organization</span>\" => \"\"\n        \"<span class=sf-dump-key>api_domain</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>content_generation</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>product_short_description_prompt</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#35831;&#29983;&#25104;&#31616;&#21333;&#30340;&#19968;&#21477;&#35805;&#25551;&#36848;</span>\"\n        \"<span class=sf-dump-key>product_description_prompt</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#35831;&#20248;&#21270;&#29983;&#25104;&#20135;&#21697;&#25551;&#36848;&#12290;</span>\"\n        \"<span class=sf-dump-key>category_description_prompt</span>\" => \"\"\n        \"<span class=sf-dump-key>cms_page_content_prompt</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#35831;&#20248;&#21270;&#20869;&#23481;&#25551;&#36848;&#12290;</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>image_generation</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>review_translation</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>checkout_message</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n        \"<span class=sf-dump-key>prompt</span>\" => \"\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372303281\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2086860738 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IlI3Ynp1amlLdGpJY0RiYnVxbjRBSXc9PSIsInZhbHVlIjoid3ZRTkx2SW1ad05NaHA2RWZETUIrSDFLME5kRlMySXVyVFZVd0NRdVJteCtnQkxoc3d5K2VPQy91SnNCSFlBKzdvRTFWbllIdkc0cDk1YTFOWmI1WmFaMFgzMTNQN2J3ZmFkMytHTW9XQXdmWXQwbnltUDR0QWZXQjR1Mk1wbWwiLCJtYWMiOiI0MzhjOGIwYWIyNTkxZDE0MmM4NDM0ZjNlMzI1YmI2NDk0NWUxNzY0ZTlhOTEyN2I4N2M4YWY5OThjNWU4OTNmIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IlhZNWZrMEJ2TmpxajRaRTd3QmtUMlE9PSIsInZhbHVlIjoiSzgzSkI0NUxnUlk4MHljdHBkWWg5YUQ3VUdqUWtJRTBFUDRibytQcmZDVm1nRWdsazhnQlZoWTVmeWhoeHlxRmN3TDVQa0EwcE8waUxuL20rK1U5WXFnSkI4akZBbnhkWHk3cW1zS3R4ckYzS1VnR21zcTIvUGsrdVVqVjQrdlIiLCJtYWMiOiI5OTM5MDg4ZjM5ZmI4ZjBmZTI3NzkyYzc4NTBmNTk4MWM5OTliZjc1NTMyMjQ5Y2UwYTNlZDEwZjQ0ZDZlY2JkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://mlk.test/admin/configuration/general/magic_ai</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundarycz07ynAnsMQYnNNQ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">31667</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2086860738\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-407363376 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mRElLYzaB8skVInGoMo42QqvMAEc6r8ga9pHAY8N</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-407363376\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1751050059 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:17:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://mlk.test/admin/configuration/general/magic_ai</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1751050059\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-130170279 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://mlk.test/admin/configuration/general/magic_ai</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#37197;&#32622;&#20445;&#23384;&#25104;&#21151;</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-130170279\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/configuration/general/magic_ai", "action_name": "admin.configuration.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store"}, "badge": "302 Found"}}