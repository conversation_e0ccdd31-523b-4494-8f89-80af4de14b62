{"__meta": {"id": "01K1G34R79A5EF3QH5BHQXV3D9", "datetime": "2025-07-31 12:16:11", "utime": **********.11467, "method": "POST", "uri": "/admin/configuration/general/magic_ai", "ip": "127.0.0.1"}, "modules": {"count": 2, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\CoreConfig (26)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.25, "duration_str": "250ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.enabled' and `channel_code` = 'default'", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 22 limit 1", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_key' and `channel_code` = 'default'", "duration": 0.42, "duration_str": "420ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 23 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `core_config` set `value` = '********************************************************************************************************************************************************************', `core_config`.`updated_at` = '2025-07-31 12:16:10' where `id` = 23", "duration": 4.08, "duration_str": "4.08s", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.organization' and `channel_code` = 'default'", "duration": 0.46, "duration_str": "460ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 24 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_domain' and `channel_code` = 'default'", "duration": 0.5, "duration_str": "500ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 25 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "update `core_config` set `value` = '', `core_config`.`updated_at` = '2025-07-31 12:16:10' where `id` = 25", "duration": 2.36, "duration_str": "2.36s", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.enabled'", "duration": 0.46, "duration_str": "460ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 26 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_short_description_prompt' and `locale_code` = 'en'", "duration": 0.57, "duration_str": "570ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 35 limit 1", "duration": 0.34, "duration_str": "340ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_description_prompt' and `locale_code` = 'en'", "duration": 0.48, "duration_str": "480ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 36 limit 1", "duration": 0.29, "duration_str": "290ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.category_description_prompt' and `locale_code` = 'en'", "duration": 0.48, "duration_str": "480ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 37 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.cms_page_content_prompt' and `locale_code` = 'en'", "duration": 0.43, "duration_str": "430ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 38 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.image_generation.enabled' and `channel_code` = 'default'", "duration": 0.48, "duration_str": "480ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 31 limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.review_translation.enabled' and `channel_code` = 'default'", "duration": 0.49, "duration_str": "490ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 32 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.enabled' and `channel_code` = 'default'", "duration": 0.48, "duration_str": "480ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 33 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.prompt' and `locale_code` = 'en' and `channel_code` = 'default'", "duration": 0.48, "duration_str": "480ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 39 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.13, "duration_str": "130ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.601, "end": **********.121728, "duration": 1.5207278728485107, "duration_str": "1.52s", "measures": [{"label": "Booting", "start": **********.601, "relative_start": 0, "end": **********.788075, "relative_end": **********.788075, "duration": 0.*****************, "duration_str": "187ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.788086, "relative_start": 0.*****************, "end": **********.12173, "relative_end": 2.1457672119140625e-06, "duration": 1.***************, "duration_str": "1.33s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.799761, "relative_start": 0.***************, "end": **********.802432, "relative_end": **********.802432, "duration": 0.0026710033416748047, "duration_str": "2.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.11202, "relative_start": 1.****************, "end": **********.112364, "relative_end": **********.112364, "duration": 0.0003440380096435547, "duration_str": "344μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 35, "nb_statements": 35, "nb_visible_statements": 35, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.016550000000000002, "accumulated_duration_str": "16.55ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.814738, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 2.356}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.818091, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 2.356, "width_percent": 1.088}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.823983, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 3.444, "width_percent": 1.511}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.825837, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 4.955, "width_percent": 1.208}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.826816, "duration": 0.00013000000000000002, "duration_str": "130μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 6.163, "width_percent": 0.785}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.830101, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 6.949, "width_percent": 1.148}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.832186, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 8.097, "width_percent": 0.906}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.8354871, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 9.003, "width_percent": 2.054}, {"sql": "select * from `core_config` where `core_config`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.836965, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 11.057, "width_percent": 1.208}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_key' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.api_key", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.0154078, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 12.266, "width_percent": 2.538}, {"sql": "select * from `core_config` where `core_config`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.018679, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 14.804, "width_percent": 1.088}, {"sql": "update `core_config` set `value` = '********************************************************************************************************************************************************************', `core_config`.`updated_at` = '2025-07-31 12:16:10' where `id` = 23", "type": "query", "params": [], "bindings": ["********************************************************************************************************************************************************************", "2025-07-31 12:16:10", 23], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.020185, "duration": 0.00408, "duration_str": "4.08ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 15.891, "width_percent": 24.653}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.organization' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.organization", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.112107, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 40.544, "width_percent": 2.779}, {"sql": "select * from `core_config` where `core_config`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.113654, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 43.323, "width_percent": 1.027}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_domain' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.api_domain", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.204001, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 44.35, "width_percent": 3.021}, {"sql": "select * from `core_config` where `core_config`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.205498, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 47.372, "width_percent": 1.088}, {"sql": "update `core_config` set `value` = '', `core_config`.`updated_at` = '2025-07-31 12:16:10' where `id` = 25", "type": "query", "params": [], "bindings": ["", "2025-07-31 12:16:10", 25], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.206822, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:700", "source": {"index": 14, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 700}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=700", "ajax": false, "filename": "BaseRepository.php", "line": "700"}, "connection": "mlk", "explain": null, "start_percent": 48.459, "width_percent": 14.26}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.enabled'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2977061, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:79", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=79", "ajax": false, "filename": "CoreConfigRepository.php", "line": "79"}, "connection": "mlk", "explain": null, "start_percent": 62.719, "width_percent": 2.779}, {"sql": "select * from `core_config` where `core_config`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.29913, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 65.498, "width_percent": 0.967}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_short_description_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.product_short_description_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.3998818, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 66.465, "width_percent": 3.444}, {"sql": "select * from `core_config` where `core_config`.`id` = 35 limit 1", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.401701, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 69.909, "width_percent": 2.054}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_description_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.product_description_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.493973, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 71.964, "width_percent": 2.9}, {"sql": "select * from `core_config` where `core_config`.`id` = 36 limit 1", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.495398, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 74.864, "width_percent": 1.752}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.category_description_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.category_description_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.581651, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 76.616, "width_percent": 2.9}, {"sql": "select * from `core_config` where `core_config`.`id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.58308, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 79.517, "width_percent": 0.967}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.cms_page_content_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.cms_page_content_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.6692188, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 80.483, "width_percent": 2.598}, {"sql": "select * from `core_config` where `core_config`.`id` = 38 limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.670653, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 83.082, "width_percent": 0.967}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.image_generation.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.image_generation.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.75631, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 84.048, "width_percent": 2.9}, {"sql": "select * from `core_config` where `core_config`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.757791, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 86.949, "width_percent": 1.39}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.review_translation.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.review_translation.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.843308, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 88.338, "width_percent": 2.961}, {"sql": "select * from `core_config` where `core_config`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.844779, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 91.299, "width_percent": 1.027}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.checkout_message.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.9298, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 92.326, "width_percent": 2.9}, {"sql": "select * from `core_config` where `core_config`.`id` = 33 limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.931233, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 95.227, "width_percent": 1.027}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.prompt' and `locale_code` = 'en' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.checkout_message.prompt", "en", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 63}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.0201921, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:63", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=63", "ajax": false, "filename": "CoreConfigRepository.php", "line": "63"}, "connection": "mlk", "explain": null, "start_percent": 96.254, "width_percent": 2.9}, {"sql": "select * from `core_config` where `core_config`.`id` = 39 limit 1", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.021718, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 99.154, "width_percent": 0.846}]}, "models": {"data": {"Webkul\\Core\\Models\\CoreConfig": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCoreConfig.php&line=1", "ajax": false, "filename": "CoreConfig.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 29, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/configuration/general/magic_ai", "action_name": "admin.configuration.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store", "uri": "POST admin/configuration/{slug?}/{slug2?}", "controller": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FConfigurationController.php&line=53\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/configuration/{slug?}/{slug2?}", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FConfigurationController.php&line=53\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php:53-128</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "1.52s", "peak_memory": "42MB", "response": "Redirect to http://mlk.test/admin/configuration/general/magic_ai", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-349050662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-349050662\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1550548175 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>channel</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>keys</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"582 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.image-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u56fe\\u50cf\\u4e0a\\u4f20\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528DALL-E\\u751f\\u6210\\u56fe\\u50cf\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u56fe\\u50cf\\u4e0a\\u4f20\\u751f\\u6210\\u56fe\\u50cf\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.image_generation&quot;,&quot;name&quot;:&quot;\\u56fe\\u50cf\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"3273 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama-groq&quot;,&quot;value&quot;:&quot;llama3-8b-8192&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u6216\\u8bbf\\u5ba2\\u63d0\\u4f9b\\u5c06\\u5ba2\\u6237\\u8bc4\\u8bba\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u7684\\u9009\\u9879\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u8bc4\\u8bba\\u9875\\u9762\\uff0c\\u5982\\u679c\\u8bc4\\u8bba\\u4e0d\\u662f\\u82f1\\u8bed\\uff0c\\u60a8\\u4f1a\\u627e\\u5230\\u201c\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u201d\\u6309\\u94ae\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.review_translation&quot;,&quot;name&quot;:&quot;\\u8bc4\\u8bba\\u7ffb\\u8bd1&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3273 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama-groq&quot;,&quot;value&quot;:&quot;llama3-8b-8192&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u6216\\u8bbf\\u5ba2\\u63d0\\u4f9b\\u5c06\\u5ba2\\u6237\\u8bc4\\u8bba\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u7684\\u9009\\u9879\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u8bc4\\u8bba\\u9875\\u9762\\uff0c\\u5982\\u679c\\u8bc4\\u8bba\\u4e0d\\u662f\\u82f1\\u8bed\\uff0c\\u60a8\\u4f1a\\u627e\\u5230\\u201c\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u201d\\u6309\\u94ae\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.review_translation&quot;,&quot;name&quot;:&quot;\\u8bc4\\u8bba\\u7ffb\\u8bd1&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>general</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>magic_ai</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>settings</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>api_key</span>\" => \"<span class=sf-dump-str title=\"164 characters\">********************************************************************************************************************************************************************</span>\"\n        \"<span class=sf-dump-key>organization</span>\" => \"\"\n        \"<span class=sf-dump-key>api_domain</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>content_generation</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>product_short_description_prompt</span>\" => \"\"\n        \"<span class=sf-dump-key>product_description_prompt</span>\" => \"\"\n        \"<span class=sf-dump-key>category_description_prompt</span>\" => \"\"\n        \"<span class=sf-dump-key>cms_page_content_prompt</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>image_generation</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>review_translation</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>checkout_message</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n        \"<span class=sf-dump-key>prompt</span>\" => \"\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1550548175\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-406663976 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6IklWZkxOdDRqOXdRN09JNEN4VldRTnc9PSIsInZhbHVlIjoiYmxYZGw1NzdXT0lCSXdOMjFoNmNSZTVUZkpWcEF2WWQxNzdjWkdxZWtJMVFoZGxSQ1RXeTN6WTN3NzhqcEhibXJnaW4vTWl4NmpvT25sMHVTaEkzSWdlaVh5dTlHdSt6SVV3dGE4K2prUDFEdnlMcy8xbVhmNlBxWEhWb2VpeVoiLCJtYWMiOiI1NDA4Nzg1Y2NkZTEyNjkxMzJiZjYxM2I1ZTNiNDJhZWY0NzdmNGVjMDg2MjVjZGExMTEzNjU4YmUyODkyODhlIiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6InFIVGV0ZFV5Z1JuVzJSbmgrKzJUT2c9PSIsInZhbHVlIjoiNkV6dEJETUd1K0FpL1V1QnNkeDV3MWxEbjNuK2kwRHRDNXBYN3NXaHpEcVV4R3ZSMGY0Y1ZjZjJ3UHZ1T0lGeHp6enRWR21TNHJZdjU1clhMck1LUkVjSndodkRDS0xUYStmK3prT2taN0k1enBZME5kcHpjOUNMeElNOFU2TTMiLCJtYWMiOiJhMjU1MjRjM2VmYzFmZTQ3NzY4MjVhYmE0Nzg4MGM0NDMxYzNhOWYxMTEzNWMzY2ZiMWNlNDkyYzZiMzVjYTY1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://mlk.test/admin/configuration/general/magic_ai</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryed2KIai4lnekQ0Jy</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">31580</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-406663976\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1918349257 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mRElLYzaB8skVInGoMo42QqvMAEc6r8ga9pHAY8N</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1918349257\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1800845443 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:16:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://mlk.test/admin/configuration/general/magic_ai</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1800845443\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-635165057 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://mlk.test/admin/configuration/general/magic_ai</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#37197;&#32622;&#20445;&#23384;&#25104;&#21151;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635165057\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/configuration/general/magic_ai", "action_name": "admin.configuration.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store"}, "badge": "302 Found"}}