{"__meta": {"id": "01K1G33V3Q5NEYMS0H8E20V6FK", "datetime": "2025-07-31 12:15:41", "utime": **********.303492, "method": "POST", "uri": "/admin/configuration/general/magic_ai", "ip": "127.0.0.1"}, "modules": {"count": 0, "modules": []}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753960540.990578, "end": **********.31468, "duration": 0.32410216331481934, "duration_str": "324ms", "measures": [{"label": "Booting", "start": 1753960540.990578, "relative_start": 0, "end": **********.263415, "relative_end": **********.263415, "duration": 0.*****************, "duration_str": "273ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.263425, "relative_start": 0.*****************, "end": **********.314682, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "51.26ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.278576, "relative_start": 0.****************, "end": **********.28128, "relative_end": **********.28128, "duration": 0.002704143524169922, "duration_str": "2.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: errors::419", "start": **********.298238, "relative_start": 0.****************, "end": **********.298238, "relative_end": **********.298238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: errors::minimal", "start": **********.300142, "relative_start": 0.*****************, "end": **********.300142, "relative_end": **********.300142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.301678, "relative_start": 0.****************, "end": **********.301862, "relative_end": **********.301862, "duration": 0.00018405914306640625, "duration_str": "184μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 34645448, "peak_usage_str": "33MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Illuminate\\Session\\TokenMismatchException", "message": "CSRF token mismatch.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php", "line": 95, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-1102564465 data-indent-pad=\"  \"><span class=sf-dump-note>array:38</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>75</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>807</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>786</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">packages/Webkul/Installer/src/Http/Middleware/CanInstall.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>30</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Webkul\\Installer\\Http\\Middleware\\CanInstall</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">packages/Webkul/Core/src/Http/Middleware/SecureHeaders.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>29</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Webkul\\Core\\Http\\Middleware\\SecureHeaders</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1102564465\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            });\n", "        }\n", "\n", "        throw new TokenMismatchException('CSRF token mismatch.');\n", "    }\n", "\n", "    /**\n"], "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FHttp%2FMiddleware%2FVerifyCsrfToken.php&line=95", "ajax": false, "filename": "VerifyCsrfToken.php", "line": "95"}}]}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "errors::419", "param_count": null, "params": [], "start": **********.298215, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions/views/419.blade.phperrors::419", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FExceptions%2Fviews%2F419.blade.php&line=1", "ajax": false, "filename": "419.blade.php", "line": "?"}}, {"name": "errors::minimal", "param_count": null, "params": [], "start": **********.300118, "type": "blade", "hash": "bladeE:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Exceptions/views/minimal.blade.phperrors::minimal", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FFoundation%2FExceptions%2Fviews%2Fminimal.blade.php&line=1", "ajax": false, "filename": "minimal.blade.php", "line": "?"}}]}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "419 ", "full_url": "http://mlk.test/admin/configuration/general/magic_ai", "action_name": "admin.configuration.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store", "uri": "POST admin/configuration/{slug?}/{slug2?}", "controller": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FConfigurationController.php&line=53\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/configuration/{slug?}/{slug2?}", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FConfigurationController.php&line=53\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php:53-128</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "331ms", "peak_memory": "38MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1873253770 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1873253770\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1969399509 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">iIW40IRBdM91Sy5gjkbEHm2Zi9R07kCGbnWlZEBS</span>\"\n  \"<span class=sf-dump-key>channel</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>keys</span>\" => <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"582 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.image-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u56fe\\u50cf\\u4e0a\\u4f20\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528DALL-E\\u751f\\u6210\\u56fe\\u50cf\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u56fe\\u50cf\\u4e0a\\u4f20\\u751f\\u6210\\u56fe\\u50cf\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.image_generation&quot;,&quot;name&quot;:&quot;\\u56fe\\u50cf\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"3273 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama-groq&quot;,&quot;value&quot;:&quot;llama3-8b-8192&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u6216\\u8bbf\\u5ba2\\u63d0\\u4f9b\\u5c06\\u5ba2\\u6237\\u8bc4\\u8bba\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u7684\\u9009\\u9879\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u8bc4\\u8bba\\u9875\\u9762\\uff0c\\u5982\\u679c\\u8bc4\\u8bba\\u4e0d\\u662f\\u82f1\\u8bed\\uff0c\\u60a8\\u4f1a\\u627e\\u5230\\u201c\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u201d\\u6309\\u94ae\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.review_translation&quot;,&quot;name&quot;:&quot;\\u8bc4\\u8bba\\u7ffb\\u8bd1&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3273 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama-groq&quot;,&quot;value&quot;:&quot;llama3-8b-8192&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u6216\\u8bbf\\u5ba2\\u63d0\\u4f9b\\u5c06\\u5ba2\\u6237\\u8bc4\\u8bba\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u7684\\u9009\\u9879\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u8bc4\\u8bba\\u9875\\u9762\\uff0c\\u5982\\u679c\\u8bc4\\u8bba\\u4e0d\\u662f\\u82f1\\u8bed\\uff0c\\u60a8\\u4f1a\\u627e\\u5230\\u201c\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u201d\\u6309\\u94ae\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.review_translation&quot;,&quot;name&quot;:&quot;\\u8bc4\\u8bba\\u7ffb\\u8bd1&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>general</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>magic_ai</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>settings</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>api_key</span>\" => \"<span class=sf-dump-str title=\"164 characters\">********************************************************************************************************************************************************************</span>\"\n        \"<span class=sf-dump-key>organization</span>\" => \"\"\n        \"<span class=sf-dump-key>api_domain</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>content_generation</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>product_short_description_prompt</span>\" => \"\"\n        \"<span class=sf-dump-key>product_description_prompt</span>\" => \"\"\n        \"<span class=sf-dump-key>category_description_prompt</span>\" => \"\"\n        \"<span class=sf-dump-key>cms_page_content_prompt</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>image_generation</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>review_translation</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>checkout_message</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n        \"<span class=sf-dump-key>prompt</span>\" => \"\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1969399509\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1256985294 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">sidebar_collapsed=0; dark_mode=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://mlk.test/admin/configuration/general/magic_ai</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryDA0fOALibP66rLtT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">31580</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1256985294\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1123204121 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1123204121\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-297356210 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:15:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"439 characters\">mlk_session=eyJpdiI6InBaL0pQMDM4elpVZ0k0R1NRWTBxUGc9PSIsInZhbHVlIjoid2NhdjI0WlN1WHJaaGFuRHc1UllsZVd1VHVuS0x4TU5US1FkbDVpaGxYNjZlUjFSK0RQTUZFSmVHK3Y5TldzVlgybEpla1RBd2dpcXIyRks2bWozc3JNQzR6QVoxenRibUIrWVN4UWFQZk5QaHpvaEVlUFNLZ0dyNkF3SHJRY28iLCJtYWMiOiIzNWY5ZGNmNmE3MjVlZjI4NWI1OTRjZmFhMjAxZmE3ODNhYjhjMzgyYTY3MDU4ZDkwYmM3NTI2Y2EwZjRlYmFmIiwidGFnIjoiIn0%3D; expires=Thu, 31 Jul 2025 13:15:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"411 characters\">mlk_session=eyJpdiI6InBaL0pQMDM4elpVZ0k0R1NRWTBxUGc9PSIsInZhbHVlIjoid2NhdjI0WlN1WHJaaGFuRHc1UllsZVd1VHVuS0x4TU5US1FkbDVpaGxYNjZlUjFSK0RQTUZFSmVHK3Y5TldzVlgybEpla1RBd2dpcXIyRks2bWozc3JNQzR6QVoxenRibUIrWVN4UWFQZk5QaHpvaEVlUFNLZ0dyNkF3SHJRY28iLCJtYWMiOiIzNWY5ZGNmNmE3MjVlZjI4NWI1OTRjZmFhMjAxZmE3ODNhYjhjMzgyYTY3MDU4ZDkwYmM3NTI2Y2EwZjRlYmFmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:15:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-297356210\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1141658177 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1141658177\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "419 ", "full_url": "http://mlk.test/admin/configuration/general/magic_ai", "action_name": "admin.configuration.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store"}, "badge": "419 "}}