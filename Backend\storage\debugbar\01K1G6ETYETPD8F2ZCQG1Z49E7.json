{"__meta": {"id": "01K1G6ETYETPD8F2ZCQG1Z49E7", "datetime": "2025-07-31 13:14:07", "utime": **********.311538, "method": "POST", "uri": "/admin/configuration/general/magic_ai", "ip": "127.0.0.1"}, "modules": {"count": 2, "modules": [{"name": "Webkul\\Core", "models": ["Webkul\\Core\\Models\\Channel (1)", "Webkul\\Core\\Models\\CoreConfig (26)"], "views": [], "queries": [{"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "duration": 0.28, "duration_str": "280ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.enabled' and `channel_code` = 'default'", "duration": 0.39, "duration_str": "390ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 22 limit 1", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_key' and `channel_code` = 'default'", "duration": 0.5, "duration_str": "500ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 23 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.organization' and `channel_code` = 'default'", "duration": 0.51, "duration_str": "510ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 24 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_domain' and `channel_code` = 'default'", "duration": 0.43, "duration_str": "430ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 25 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.enabled'", "duration": 0.47, "duration_str": "470ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 26 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_short_description_prompt' and `locale_code` = 'en'", "duration": 0.49, "duration_str": "490ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 35 limit 1", "duration": 0.17, "duration_str": "170ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_description_prompt' and `locale_code` = 'en'", "duration": 0.42, "duration_str": "420ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 36 limit 1", "duration": 0.18, "duration_str": "180ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.category_description_prompt' and `locale_code` = 'en'", "duration": 0.51, "duration_str": "510ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 37 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.cms_page_content_prompt' and `locale_code` = 'en'", "duration": 0.45, "duration_str": "450ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 38 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.image_generation.enabled' and `channel_code` = 'default'", "duration": 0.44, "duration_str": "440ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 31 limit 1", "duration": 0.22, "duration_str": "220ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.review_translation.enabled' and `channel_code` = 'default'", "duration": 0.42, "duration_str": "420ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 32 limit 1", "duration": 0.14, "duration_str": "140ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.product_translation.enabled' and `channel_code` = 'default'", "duration": 0.47, "duration_str": "470ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.product_translation.source_locale' and `channel_code` = 'default'", "duration": 0.5, "duration_str": "500ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.product_translation.model' and `channel_code` = 'default'", "duration": 0.46, "duration_str": "460ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.enabled' and `channel_code` = 'default'", "duration": 0.48, "duration_str": "480ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 33 limit 1", "duration": 0.23, "duration_str": "230ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.prompt' and `locale_code` = 'en' and `channel_code` = 'default'", "duration": 0.48, "duration_str": "480ms", "connection": "mlk"}, {"sql": "select * from `core_config` where `core_config`.`id` = 39 limit 1", "duration": 0.21, "duration_str": "210ms", "connection": "mlk"}]}, {"name": "Webkul\\User", "models": ["Webkul\\User\\Models\\Admin (1)", "Webkul\\User\\Models\\Role (1)"], "views": [], "queries": [{"sql": "select count(*) as aggregate from `admins`", "duration": 0.2, "duration_str": "200ms", "connection": "mlk"}, {"sql": "select count(*) as aggregate from `admins`", "duration": 0.15, "duration_str": "150ms", "connection": "mlk"}, {"sql": "select * from `admins` where `id` = 1 limit 1", "duration": 0.19, "duration_str": "190ms", "connection": "mlk"}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "duration": 0.16, "duration_str": "160ms", "connection": "mlk"}]}]}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.50243, "end": **********.318641, "duration": 1.8162109851837158, "duration_str": "1.82s", "measures": [{"label": "Booting", "start": **********.50243, "relative_start": 0, "end": **********.793474, "relative_end": **********.793474, "duration": 0.****************, "duration_str": "291ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.793486, "relative_start": 0.*****************, "end": **********.318643, "relative_end": 2.1457672119140625e-06, "duration": 1.****************, "duration_str": "1.53s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.808122, "relative_start": 0.****************, "end": **********.810786, "relative_end": **********.810786, "duration": 0.0026640892028808594, "duration_str": "2.66ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.308928, "relative_start": 1.****************, "end": **********.309239, "relative_end": **********.309239, "duration": 0.0003108978271484375, "duration_str": "311μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.2.0", "Environment": "local", "Debug Mode": "Enabled", "URL": "mlk.test", "Timezone": "Africa/Lagos", "Locale": "zh_CN"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 39, "nb_statements": 39, "nb_visible_statements": 39, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01979, "accumulated_duration_str": "19.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.8260002, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 0, "width_percent": 2.274}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 832}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 1078}], "start": **********.830368, "duration": 0.0002, "duration_str": "200μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 2.274, "width_percent": 1.011}, {"sql": "select * from `channels` where `hostname` in ('mlk.test', 'http://mlk.test', 'https://mlk.test')", "type": "query", "params": [], "bindings": ["mlk.test", "http://mlk.test", "https://mlk.test"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Core.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Core.php", "line": 143}, {"index": 17, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 127}, {"index": 18, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 45}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 1004}], "start": **********.83777, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:578", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 578}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=578", "ajax": false, "filename": "BaseRepository.php", "line": "578"}, "connection": "mlk", "explain": null, "start_percent": 3.284, "width_percent": 1.415}, {"sql": "select exists (select 1 from information_schema.tables where table_schema = 'mlk' and table_name = 'admins' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, {"index": 14, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 16, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.8400168, "duration": 0.00023999999999999998, "duration_str": "240μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:32", "source": {"index": 13, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=32", "ajax": false, "filename": "DatabaseManager.php", "line": "32"}, "connection": "mlk", "explain": null, "start_percent": 4.699, "width_percent": 1.213}, {"sql": "select count(*) as aggregate from `admins`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 18, "namespace": "middleware", "name": "admin_locale", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Middleware\\AdminLocale.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.8414161, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "DatabaseManager.php:38", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Installer/src/Helpers/DatabaseManager.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Installer\\src\\Helpers\\DatabaseManager.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FInstaller%2Fsrc%2FHelpers%2FDatabaseManager.php&line=38", "ajax": false, "filename": "DatabaseManager.php", "line": "38"}, "connection": "mlk", "explain": null, "start_percent": 5.912, "width_percent": 0.758}, {"sql": "select * from `admins` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 18}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.845454, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "mlk", "explain": null, "start_percent": 6.67, "width_percent": 0.96}, {"sql": "select * from `roles` where `roles`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, {"index": 22, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 36}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}, {"index": 24, "namespace": null, "name": "packages/Webkul/Core/src/Http/Middleware/PreventRequestsDuringMaintenance.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Http\\Middleware\\PreventRequestsDuringMaintenance.php", "line": 119}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.847977, "duration": 0.00016, "duration_str": "160μs", "memory": 0, "memory_str": null, "filename": "admin:54", "source": {"index": 21, "namespace": "middleware", "name": "admin", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\User\\src\\Http\\Middleware\\Bouncer.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FHttp%2FMiddleware%2FBouncer.php&line=54", "ajax": false, "filename": "Bouncer.php", "line": "54"}, "connection": "mlk", "explain": null, "start_percent": 7.63, "width_percent": 0.808}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.851532, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 8.439, "width_percent": 1.971}, {"sql": "select * from `core_config` where `core_config`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.853096, "duration": 0.00015, "duration_str": "150μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 10.409, "width_percent": 0.758}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_key' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.api_key", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.951533, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 11.167, "width_percent": 2.527}, {"sql": "select * from `core_config` where `core_config`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.9547899, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 13.694, "width_percent": 1.112}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.organization' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.organization", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.044704, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 14.805, "width_percent": 2.577}, {"sql": "select * from `core_config` where `core_config`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.046284, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 17.383, "width_percent": 0.859}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.settings.api_domain' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.settings.api_domain", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.132782, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 18.242, "width_percent": 2.173}, {"sql": "select * from `core_config` where `core_config`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.134324, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 20.414, "width_percent": 0.859}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.enabled'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.enabled"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.219353, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:79", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 79}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=79", "ajax": false, "filename": "CoreConfigRepository.php", "line": "79"}, "connection": "mlk", "explain": null, "start_percent": 21.273, "width_percent": 2.375}, {"sql": "select * from `core_config` where `core_config`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.2208781, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 23.648, "width_percent": 0.859}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_short_description_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.product_short_description_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.30862, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 24.507, "width_percent": 2.476}, {"sql": "select * from `core_config` where `core_config`.`id` = 35 limit 1", "type": "query", "params": [], "bindings": [35], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.310083, "duration": 0.00017, "duration_str": "170μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 26.983, "width_percent": 0.859}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.product_description_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.product_description_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.397557, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 27.842, "width_percent": 2.122}, {"sql": "select * from `core_config` where `core_config`.`id` = 36 limit 1", "type": "query", "params": [], "bindings": [36], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.398888, "duration": 0.00017999999999999998, "duration_str": "180μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 29.965, "width_percent": 0.91}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.category_description_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.category_description_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.483921, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 30.874, "width_percent": 2.577}, {"sql": "select * from `core_config` where `core_config`.`id` = 37 limit 1", "type": "query", "params": [], "bindings": [37], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.485484, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 33.451, "width_percent": 0.96}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.content_generation.cms_page_content_prompt' and `locale_code` = 'en'", "type": "query", "params": [], "bindings": ["general.magic_ai.content_generation.cms_page_content_prompt", "en"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.580651, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:75", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 75}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=75", "ajax": false, "filename": "CoreConfigRepository.php", "line": "75"}, "connection": "mlk", "explain": null, "start_percent": 34.411, "width_percent": 2.274}, {"sql": "select * from `core_config` where `core_config`.`id` = 38 limit 1", "type": "query", "params": [], "bindings": [38], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.582262, "duration": 0.00019, "duration_str": "190μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 36.685, "width_percent": 0.96}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.image_generation.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.image_generation.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.673248, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 37.645, "width_percent": 2.223}, {"sql": "select * from `core_config` where `core_config`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.674834, "duration": 0.00022, "duration_str": "220μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 39.869, "width_percent": 1.112}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.review_translation.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.review_translation.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.762991, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 40.98, "width_percent": 2.122}, {"sql": "select * from `core_config` where `core_config`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.764439, "duration": 0.00014000000000000001, "duration_str": "140μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 43.103, "width_percent": 0.707}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.product_translation.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.product_translation.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.8493612, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 43.81, "width_percent": 2.375}, {"sql": "insert into `core_config` (`code`, `value`, `locale_code`, `channel_code`, `updated_at`, `created_at`) values ('general.magic_ai.product_translation.enabled', '1', null, 'default', '2025-07-31 13:14:06', '2025-07-31 13:14:06')", "type": "query", "params": [], "bindings": ["general.magic_ai.product_translation.enabled", "1", null, "default", "2025-07-31 13:14:06", "2025-07-31 13:14:06"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 88}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.851477, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "mlk", "explain": null, "start_percent": 46.185, "width_percent": 18.999}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.product_translation.source_locale' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.product_translation.source_locale", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.941591, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 65.184, "width_percent": 2.527}, {"sql": "insert into `core_config` (`code`, `value`, `locale_code`, `channel_code`, `updated_at`, `created_at`) values ('general.magic_ai.product_translation.source_locale', 'en', null, 'default', '2025-07-31 13:14:06', '2025-07-31 13:14:06')", "type": "query", "params": [], "bindings": ["general.magic_ai.product_translation.source_locale", "en", null, "default", "2025-07-31 13:14:06", "2025-07-31 13:14:06"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 88}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.943208, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "mlk", "explain": null, "start_percent": 67.711, "width_percent": 12.481}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.product_translation.model' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.product_translation.model", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.0308762, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 80.192, "width_percent": 2.324}, {"sql": "insert into `core_config` (`code`, `value`, `locale_code`, `channel_code`, `updated_at`, `created_at`) values ('general.magic_ai.product_translation.model', 'gpt-4o-mini', null, 'default', '2025-07-31 13:14:07', '2025-07-31 13:14:07')", "type": "query", "params": [], "bindings": ["general.magic_ai.product_translation.model", "gpt-4o-mini", null, "default", "2025-07-31 13:14:07", "2025-07-31 13:14:07"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, {"index": 16, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 88}, {"index": 17, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.0323482, "duration": 0.00206, "duration_str": "2.06ms", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:651", "source": {"index": 15, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 651}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=651", "ajax": false, "filename": "BaseRepository.php", "line": "651"}, "connection": "mlk", "explain": null, "start_percent": 82.516, "width_percent": 10.409}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.enabled' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.checkout_message.enabled", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.118583, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:68", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 68}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=68", "ajax": false, "filename": "CoreConfigRepository.php", "line": "68"}, "connection": "mlk", "explain": null, "start_percent": 92.926, "width_percent": 2.425}, {"sql": "select * from `core_config` where `core_config`.`id` = 33 limit 1", "type": "query", "params": [], "bindings": [33], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.1200268, "duration": 0.00023, "duration_str": "230μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 95.351, "width_percent": 1.162}, {"sql": "select * from `core_config` where `code` = 'general.magic_ai.checkout_message.prompt' and `locale_code` = 'en' and `channel_code` = 'default'", "type": "query", "params": [], "bindings": ["general.magic_ai.checkout_message.prompt", "en", "default"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 63}, {"index": 16, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.21073, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CoreConfigRepository.php:63", "source": {"index": 15, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FRepositories%2FCoreConfigRepository.php&line=63", "ajax": false, "filename": "CoreConfigRepository.php", "line": "63"}, "connection": "mlk", "explain": null, "start_percent": 96.513, "width_percent": 2.425}, {"sql": "select * from `core_config` where `core_config`.`id` = 39 limit 1", "type": "query", "params": [], "bindings": [39], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, {"index": 21, "namespace": null, "name": "packages/Webkul/Core/src/Repositories/CoreConfigRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Core\\src\\Repositories\\CoreConfigRepository.php", "line": 103}, {"index": 22, "namespace": null, "name": "packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\packages\\Webkul\\Admin\\src\\Http\\Controllers\\ConfigurationController.php", "line": 123}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}], "start": **********.212281, "duration": 0.00020999999999999998, "duration_str": "210μs", "memory": 0, "memory_str": null, "filename": "BaseRepository.php:695", "source": {"index": 20, "namespace": null, "name": "vendor/prettus/l5-repository/src/Prettus/Repository/Eloquent/BaseRepository.php", "file": "E:\\FlyEnv\\php\\mlk\\bagisto\\Backend\\vendor\\prettus\\l5-repository\\src\\Prettus\\Repository\\Eloquent\\BaseRepository.php", "line": 695}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fvendor%2Fprettus%2Fl5-repository%2Fsrc%2FPrettus%2FRepository%2FEloquent%2FBaseRepository.php&line=695", "ajax": false, "filename": "BaseRepository.php", "line": "695"}, "connection": "mlk", "explain": null, "start_percent": 98.939, "width_percent": 1.061}]}, "models": {"data": {"Webkul\\Core\\Models\\CoreConfig": {"value": 26, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FCoreConfig.php&line=1", "ajax": false, "filename": "CoreConfig.php", "line": "?"}}, "Webkul\\Core\\Models\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FCore%2Fsrc%2FModels%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "Webkul\\User\\Models\\Admin": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FAdmin.php&line=1", "ajax": false, "filename": "Admin.php", "line": "?"}}, "Webkul\\User\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FUser%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 29, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "302 Found", "full_url": "http://mlk.test/admin/configuration/general/magic_ai", "action_name": "admin.configuration.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store", "uri": "POST admin/configuration/{slug?}/{slug2?}", "controller": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FConfigurationController.php&line=53\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/configuration/{slug?}/{slug2?}", "file": "<a href=\"phpstorm://open?file=E%3A%2FFlyEnv%2Fphp%2Fmlk%2Fbagisto%2FBackend%2Fpackages%2FWebkul%2FAdmin%2Fsrc%2FHttp%2FControllers%2FConfigurationController.php&line=53\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">packages/Webkul/Admin/src/Http/Controllers/ConfigurationController.php:53-128</a>", "middleware": "web, admin_locale, Webkul\\Core\\Http\\Middleware\\PreventRequestsDuringMaintenance, admin, Webkul\\Core\\Http\\Middleware\\NoCacheMiddleware", "duration": "1.82s", "peak_memory": "42MB", "response": "Redirect to http://mlk.test/admin/configuration/general/magic_ai", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1772475584 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1772475584\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-158719811 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>channel</span>\" => \"<span class=sf-dump-str title=\"7 characters\">default</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>keys</span>\" => <span class=sf-dump-note>array:18</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"1010 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_key&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.api-key&quot;,&quot;type&quot;:&quot;password&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;organization&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.organization&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;api_domain&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.settings.llm-api-domain&quot;,&quot;type&quot;:&quot;text&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u901a\\u8fc7\\u8f93\\u5165\\u60a8\\u7684\\u72ec\\u5bb6API\\u5bc6\\u94a5\\u548c\\u6307\\u793a\\u76f8\\u5173\\u7ec4\\u7ec7\\u6765\\u589e\\u5f3a\\u60a8\\u4f7f\\u7528\\u9b54\\u6cd5AI\\u529f\\u80fd\\u7684\\u4f53\\u9a8c\\u3002\\u638c\\u63e1OpenAI\\u51ed\\u636e\\u5e76\\u6839\\u636e\\u60a8\\u7684\\u7279\\u5b9a\\u9700\\u6c42\\u81ea\\u5b9a\\u4e49\\u8bbe\\u7f6e\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.settings&quot;,&quot;name&quot;:&quot;\\u5e38\\u89c4\\u8bbe\\u7f6e&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"1307 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;},{&quot;name&quot;:&quot;product_short_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-short-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;product_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.product-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;category_description_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.category-description-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true},{&quot;name&quot;:&quot;cms_page_content_prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.content-generation.cms-page-content-prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u6240\\u89c1\\u5373\\u6240\\u5f97\\u7f16\\u8f91\\u5668\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528AI\\u7ba1\\u7406\\u5185\\u5bb9\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u7f16\\u8f91\\u5668\\u751f\\u6210\\u5185\\u5bb9\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.content_generation&quot;,&quot;name&quot;:&quot;\\u5185\\u5bb9\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"582 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.image-generation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u6b64\\u529f\\u80fd\\u5c06\\u4e3a\\u6bcf\\u4e2a\\u56fe\\u50cf\\u4e0a\\u4f20\\u542f\\u7528\\u9b54\\u6cd5AI\\uff0c\\u60a8\\u53ef\\u4ee5\\u4f7f\\u7528DALL-E\\u751f\\u6210\\u56fe\\u50cf\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u4efb\\u4f55\\u56fe\\u50cf\\u4e0a\\u4f20\\u751f\\u6210\\u56fe\\u50cf\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.image_generation&quot;,&quot;name&quot;:&quot;\\u56fe\\u50cf\\u751f\\u6210&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"3273 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama-groq&quot;,&quot;value&quot;:&quot;llama3-8b-8192&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u6216\\u8bbf\\u5ba2\\u63d0\\u4f9b\\u5c06\\u5ba2\\u6237\\u8bc4\\u8bba\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u7684\\u9009\\u9879\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u8bc4\\u8bba\\u9875\\u9762\\uff0c\\u5982\\u679c\\u8bc4\\u8bba\\u4e0d\\u662f\\u82f1\\u8bed\\uff0c\\u60a8\\u4f1a\\u627e\\u5230\\u201c\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u201d\\u6309\\u94ae\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.review_translation&quot;,&quot;name&quot;:&quot;\\u8bc4\\u8bba\\u7ffb\\u8bd1&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"3273 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama-groq&quot;,&quot;value&quot;:&quot;llama3-8b-8192&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.review-translation.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u6216\\u8bbf\\u5ba2\\u63d0\\u4f9b\\u5c06\\u5ba2\\u6237\\u8bc4\\u8bba\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u7684\\u9009\\u9879\\u3002&lt;br\\/&gt;&lt;br\\/&gt;\\u542f\\u7528\\u540e\\uff0c\\u8f6c\\u5230\\u8bc4\\u8bba\\u9875\\u9762\\uff0c\\u5982\\u679c\\u8bc4\\u8bba\\u4e0d\\u662f\\u82f1\\u8bed\\uff0c\\u60a8\\u4f1a\\u627e\\u5230\\u201c\\u7ffb\\u8bd1\\u6210\\u82f1\\u8bed\\u201d\\u6309\\u94ae\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.review_translation&quot;,&quot;name&quot;:&quot;\\u8bc4\\u8bba\\u7ffb\\u8bd1&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"1262 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;source_locale&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.source-locale&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;default_value&quot;:&quot;en&quot;,&quot;options&quot;:[{&quot;title&quot;:&quot;English&quot;,&quot;value&quot;:&quot;en&quot;},{&quot;title&quot;:&quot;Italiano&quot;,&quot;value&quot;:&quot;it&quot;}]},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;default_value&quot;:&quot;gpt-4o-mini&quot;,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4fdd\\u5b58\\u4ea7\\u54c1\\u65f6\\u81ea\\u52a8\\u5c06\\u4ea7\\u54c1\\u4fe1\\u606f\\u7ffb\\u8bd1\\u6210\\u5176\\u4ed6\\u8bed\\u8a00\\u3002\\u4ec5\\u7ffb\\u8bd1\\u7f3a\\u5931\\u7684\\u5185\\u5bb9\\u5b57\\u6bb5\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.product_translation&quot;,&quot;name&quot;:&quot;\\u4ea7\\u54c1\\u7ffb\\u8bd1&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"1262 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;source_locale&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.source-locale&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;default_value&quot;:&quot;en&quot;,&quot;options&quot;:[{&quot;title&quot;:&quot;English&quot;,&quot;value&quot;:&quot;en&quot;},{&quot;title&quot;:&quot;Italiano&quot;,&quot;value&quot;:&quot;it&quot;}]},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;default_value&quot;:&quot;gpt-4o-mini&quot;,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4fdd\\u5b58\\u4ea7\\u54c1\\u65f6\\u81ea\\u52a8\\u5c06\\u4ea7\\u54c1\\u4fe1\\u606f\\u7ffb\\u8bd1\\u6210\\u5176\\u4ed6\\u8bed\\u8a00\\u3002\\u4ec5\\u7ffb\\u8bd1\\u7f3a\\u5931\\u7684\\u5185\\u5bb9\\u5b57\\u6bb5\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.product_translation&quot;,&quot;name&quot;:&quot;\\u4ea7\\u54c1\\u7ffb\\u8bd1&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"1262 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;source_locale&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.source-locale&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;default_value&quot;:&quot;en&quot;,&quot;options&quot;:[{&quot;title&quot;:&quot;English&quot;,&quot;value&quot;:&quot;en&quot;},{&quot;title&quot;:&quot;Italiano&quot;,&quot;value&quot;:&quot;it&quot;}]},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;default_value&quot;:&quot;gpt-4o-mini&quot;,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.product-translation.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;}]}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4fdd\\u5b58\\u4ea7\\u54c1\\u65f6\\u81ea\\u52a8\\u5c06\\u4ea7\\u54c1\\u4fe1\\u606f\\u7ffb\\u8bd1\\u6210\\u5176\\u4ed6\\u8bed\\u8a00\\u3002\\u4ec5\\u7ffb\\u8bd1\\u7f3a\\u5931\\u7684\\u5185\\u5bb9\\u5b57\\u6bb5\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.product_translation&quot;,&quot;name&quot;:&quot;\\u4ea7\\u54c1\\u7ffb\\u8bd1&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n    <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"3304 characters\">{&quot;children&quot;:[],&quot;fields&quot;:[{&quot;name&quot;:&quot;enabled&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.enabled&quot;,&quot;type&quot;:&quot;boolean&quot;,&quot;channel_based&quot;:true},{&quot;name&quot;:&quot;model&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.model&quot;,&quot;type&quot;:&quot;select&quot;,&quot;channel_based&quot;:true,&quot;options&quot;:[{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4-turbo&quot;,&quot;value&quot;:&quot;gpt-4-turbo&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o&quot;,&quot;value&quot;:&quot;gpt-4o&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gpt-4o-mini&quot;,&quot;value&quot;:&quot;gpt-4o-mini&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.gemini-2-0-flash&quot;,&quot;value&quot;:&quot;gemini-2.0-flash&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.deepseek-r1-8b&quot;,&quot;value&quot;:&quot;deepseek-r1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama-groq&quot;,&quot;value&quot;:&quot;llama3.3&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-3b&quot;,&quot;value&quot;:&quot;llama3.2:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-2-1b&quot;,&quot;value&quot;:&quot;llama3.2:1b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-1-8b&quot;,&quot;value&quot;:&quot;llama3.1:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llama3-8b&quot;,&quot;value&quot;:&quot;llama3:8b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.llava-7b&quot;,&quot;value&quot;:&quot;llava:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-13b&quot;,&quot;value&quot;:&quot;vicuna:13b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.vicuna-7b&quot;,&quot;value&quot;:&quot;vicuna:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-14b&quot;,&quot;value&quot;:&quot;qwen2.5:14b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-7b&quot;,&quot;value&quot;:&quot;qwen2.5:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-3b&quot;,&quot;value&quot;:&quot;qwen2.5:3b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-1-5b&quot;,&quot;value&quot;:&quot;qwen2.5:1.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.qwen2-5-0-5b&quot;,&quot;value&quot;:&quot;qwen2.5:0.5b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.mistral-7b&quot;,&quot;value&quot;:&quot;mistral:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.starling-lm-7b&quot;,&quot;value&quot;:&quot;starling-lm:7b&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.phi3-5&quot;,&quot;value&quot;:&quot;phi3.5&quot;},{&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.orca-mini&quot;,&quot;value&quot;:&quot;orca-mini&quot;}]},{&quot;name&quot;:&quot;prompt&quot;,&quot;title&quot;:&quot;admin::app.configuration.index.general.magic-ai.checkout-message.prompt&quot;,&quot;type&quot;:&quot;textarea&quot;,&quot;channel_based&quot;:true,&quot;locale_based&quot;:true}],&quot;icon&quot;:null,&quot;icon_class&quot;:null,&quot;info&quot;:&quot;\\u4e3a\\u5ba2\\u6237\\u5728\\u611f\\u8c22\\u9875\\u9762\\u4e0a\\u5236\\u4f5c\\u4e2a\\u6027\\u5316\\u7684\\u7ed3\\u8d26\\u6d88\\u606f\\uff0c\\u5b9a\\u5236\\u5185\\u5bb9\\u4ee5\\u7b26\\u5408\\u4e2a\\u4eba\\u504f\\u597d\\uff0c\\u589e\\u5f3a\\u6574\\u4f53\\u8d2d\\u4e70\\u540e\\u7684\\u4f53\\u9a8c\\u3002&quot;,&quot;key&quot;:&quot;general.magic_ai.checkout_message&quot;,&quot;name&quot;:&quot;\\u4e2a\\u6027\\u5316\\u7ed3\\u8d26\\u6d88\\u606f&quot;,&quot;route&quot;:null,&quot;sort&quot;:1}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>general</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>magic_ai</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>settings</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>api_key</span>\" => \"<span class=sf-dump-str title=\"164 characters\">********************************************************************************************************************************************************************</span>\"\n        \"<span class=sf-dump-key>organization</span>\" => \"\"\n        \"<span class=sf-dump-key>api_domain</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>content_generation</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>product_short_description_prompt</span>\" => \"<span class=sf-dump-str title=\"11 characters\">&#35831;&#29983;&#25104;&#31616;&#21333;&#30340;&#19968;&#21477;&#35805;&#25551;&#36848;</span>\"\n        \"<span class=sf-dump-key>product_description_prompt</span>\" => \"<span class=sf-dump-str title=\"10 characters\">&#35831;&#20248;&#21270;&#29983;&#25104;&#20135;&#21697;&#25551;&#36848;&#12290;</span>\"\n        \"<span class=sf-dump-key>category_description_prompt</span>\" => \"\"\n        \"<span class=sf-dump-key>cms_page_content_prompt</span>\" => \"<span class=sf-dump-str title=\"8 characters\">&#35831;&#20248;&#21270;&#20869;&#23481;&#25551;&#36848;&#12290;</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>image_generation</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>review_translation</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>product_translation</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>1</span>\"\n        \"<span class=sf-dump-key>source_locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n        \"<span class=sf-dump-key>model</span>\" => \"<span class=sf-dump-str title=\"11 characters\">gpt-4o-mini</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>checkout_message</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>enabled</span>\" => \"<span class=sf-dump-str>0</span>\"\n        \"<span class=sf-dump-key>prompt</span>\" => \"\"\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-158719811\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1672177535 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"743 characters\">sidebar_collapsed=0; dark_mode=0; XSRF-TOKEN=eyJpdiI6Indld09EUmNvS2VVNmlLVUl3aUUySlE9PSIsInZhbHVlIjoiQkVCTWxyMHlzdWVueG95QUhKSFF0TEFIbTh4eXpYbHEvVS9wdU9mZjN4UlkzQkIzTHFxRnVkeG5IajJDZlUwOGNSaS9OZWFmSmZKNGY1blVtdURsdzBtenpZTC9VOWpCUEwyZkZDS0gxMk92dnhXWHM3YnBqYTdnakJlSjJMckciLCJtYWMiOiI3MGEzNWQ3Y2Q5OTEwYTc2YjRhZmMzZTJiZDJhNWU3MTlmNmY1Mzg1MjYwZjBiMWJkZDkyNzE0N2IyMDg2ZjY1IiwidGFnIjoiIn0%3D; mlk_session=eyJpdiI6IlFzbUxDdGNUQ0pzcnQwQWxFeG9OK2c9PSIsInZhbHVlIjoiYkdSUU56OHVMdDZKNXByU0cvNmFTZmc2ZVZrSTB3NEZOOUVhUEFQdHVrZURkaTBDUUh6VkFJTEc2SDJOWWFpWlk4dFJsb0dOSEZuNGp4VHZFbXFmZHY5VEErNytKOEVONitEZ1ZHTTExQmF6THdHcUMyTE5NYkVVS0QzemVKdWsiLCJtYWMiOiJkZGJmODc2NjkzMTAyM2JmNWE1NTE0NDUyMzNmZDE3MTdjZWFiYTI1ZTM0MTNjMjA1NWEyOGJiMWFmZWE0YjVjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">zh-CN,zh;q=0.9,en;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://mlk.test/admin/configuration/general/magic_ai</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryvhYNzm5K3TdlLD3K</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">http://mlk.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">36287</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">mlk.test</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1672177535\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1530474454 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>sidebar_collapsed</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>dark_mode</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>mlk_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">mRElLYzaB8skVInGoMo42QqvMAEc6r8ga9pHAY8N</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1530474454\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1302357767 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 12:14:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">http://mlk.test/admin/configuration/general/magic_ai</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1302357767\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1617646098 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8JgwCiyUNd8UkqIh1rXVpF2AZtPDu8MFYLdcpQr3</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"52 characters\">http://mlk.test/admin/configuration/general/magic_ai</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_admin_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>admin_locale</span>\" => \"<span class=sf-dump-str title=\"5 characters\">zh_CN</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"6 characters\">&#37197;&#32622;&#20445;&#23384;&#25104;&#21151;</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1617646098\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://mlk.test/admin/configuration/general/magic_ai", "action_name": "admin.configuration.store", "controller_action": "Webkul\\Admin\\Http\\Controllers\\ConfigurationController@store"}, "badge": "302 Found"}}